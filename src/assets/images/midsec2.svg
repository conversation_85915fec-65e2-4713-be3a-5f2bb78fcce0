<svg width="1439" height="288" viewBox="0 0 1439 288" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1440" height="288" fill="url(#paint0_linear_0_1)"/>
<g clip-path="url(#clip0_0_1)">
<g opacity="0.4">
<mask id="mask0_0_1" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="336" y="-249" width="1428" height="812">
<g clip-path="url(#clip1_0_1)">
<path d="M401 354.082C401 354.082 528.569 309.76 587.654 313.967C646.738 318.174 673.68 408.43 715.832 389.606C748.064 375.212 829.461 319.326 874.568 335.981C996.074 380.845 1001.8 171.085 1056.75 181.22C1147.22 197.903 1071.96 375.356 1297.8 312.102C1372.52 291.173 1298.09 128.943 1358.44 110.928C1472.33 76.9314 1516.13 24.3289 1701.58 -31.0309" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.056 354.265C401.056 354.265 526.648 311.009 585.212 313.472C643.775 315.935 671.866 394.569 716.097 376.085C750.751 361.603 827.548 312.648 873.458 326.948C992.959 364.169 997.144 161.851 1054.03 170.927C1144 185.282 1072.08 361.533 1291.37 299.389C1363.84 278.869 1301.02 119.248 1361.78 101.875C1473.95 69.8072 1516.04 25.1209 1701.58 -31.0347" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.111 354.457C401.111 354.457 524.725 312.266 582.769 312.985C640.812 313.703 670.063 380.74 716.361 362.571C753.447 348.018 825.521 305.996 872.347 317.922C989.62 347.788 992.473 152.659 1051.3 160.642C1140.76 172.781 1072.2 347.718 1284.93 286.684C1355.15 266.573 1303.95 109.55 1365.13 92.8298C1475.55 62.654 1515.95 25.9207 1701.58 -31.0306" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.168 354.64C401.168 354.64 522.805 313.514 580.328 312.489C637.85 311.464 668.274 366.926 716.628 349.051C756.152 334.439 823.391 299.375 871.238 308.888C986.089 331.723 987.789 143.497 1048.58 150.349C1137.5 160.374 1072.32 333.895 1278.5 273.971C1346.46 254.268 1306.87 99.8336 1368.48 83.7767C1477.14 55.4577 1515.86 26.7128 1701.58 -31.0344" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.223 354.827C401.223 354.827 520.882 314.767 577.884 311.997C634.886 309.228 666.489 353.135 716.891 335.534C758.86 320.877 821.167 292.814 870.126 299.858C982.393 316.012 983.091 134.376 1045.85 140.06C1134.23 148.065 1072.44 320.075 1272.06 261.262C1337.78 241.968 1309.78 90.1084 1371.83 74.7274C1478.71 48.2288 1515.77 27.5086 1701.58 -31.0344" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.279 355.022C401.279 355.022 518.96 316.026 575.442 311.513C631.923 306.999 664.713 339.366 717.156 322.023C761.575 307.333 818.868 286.334 869.016 290.835C978.57 300.67 978.387 125.303 1043.13 129.778C1130.95 135.849 1072.56 306.263 1265.62 248.56C1329.09 229.674 1312.69 80.3775 1375.17 65.6843C1480.27 40.9703 1515.68 28.3111 1701.58 -31.0277" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.336 355.202C401.336 355.202 517.04 317.271 573.001 311.014C628.961 304.756 662.942 325.597 717.422 308.498C764.295 293.787 816.509 279.93 867.906 281.798C974.655 285.679 973.679 116.257 1040.4 119.482C1127.67 123.7 1072.68 292.436 1259.19 235.844C1320.41 217.366 1315.59 70.6182 1378.52 56.6276C1481.82 33.6627 1515.59 29.0996 1701.58 -31.035" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.39 355.392C401.39 355.392 515.116 318.526 570.556 310.525C625.996 302.523 660.879 310.846 717.685 294.984C767.271 281.137 814.102 273.642 866.794 272.771C970.677 271.053 968.965 107.264 1037.67 109.196C1124.39 111.633 1072.8 278.619 1252.75 223.137C1311.72 205.069 1318.48 60.8535 1381.87 47.5808C1483.36 26.3287 1515.49 29.898 1701.58 -31.0324" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.449 355.579C401.449 355.579 513.197 319.779 568.117 310.034C623.036 300.288 659.411 298.111 717.953 281.467C769.743 266.741 811.674 267.472 865.687 263.741C966.676 256.766 964.254 98.3121 1034.95 98.9067C1121.11 99.6315 1072.92 264.8 1246.32 210.429C1303.03 192.768 1321.37 51.0703 1385.22 38.5316C1484.88 18.9567 1515.41 30.6942 1701.59 -31.0322" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.505 355.768C401.505 355.768 511.275 321.033 565.674 309.543C620.073 298.054 657.649 284.383 718.217 267.951C772.47 253.232 809.235 261.435 864.576 254.713C962.674 242.797 959.543 89.4056 1032.23 88.6193C1117.83 87.6933 1073.04 250.982 1239.88 197.721C1294.35 180.469 1324.26 41.2708 1388.57 29.4835C1486.4 11.551 1515.32 31.4913 1701.59 -31.031" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.559 355.958C401.559 355.958 509.352 322.288 563.23 309.055C617.108 295.821 655.888 270.665 718.48 254.437C775.197 239.732 804.845 238.512 863.464 245.686C961.986 257.744 954.833 80.5459 1029.5 78.3335C1114.55 75.8127 1073.16 237.165 1233.45 185.015C1285.66 168.171 1327.13 31.4546 1391.91 20.4373C1487.9 4.11167 1515.22 32.2902 1701.58 -31.0278" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.612 356.134C401.612 356.134 507.427 323.53 560.785 308.553C614.142 293.575 654.127 256.94 718.742 240.909C777.925 226.225 801.777 232.202 862.351 236.646C958.946 243.732 950.129 71.7184 1026.77 68.0336C1111.28 63.9708 1073.27 223.335 1227.01 172.295C1276.97 155.86 1330 21.6038 1395.26 11.3768C1489.39 -3.37632 1515.13 33.0753 1701.58 -31.0385" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.672 356.327C401.672 356.327 505.509 324.788 558.346 308.066C611.183 291.345 652.374 243.238 719.011 227.397C780.662 212.741 798.756 226.383 861.245 227.621C955.891 229.497 945.438 62.9531 1024.05 57.7498C1108.02 52.1918 1073.4 209.521 1220.58 159.591C1268.29 143.565 1332.87 11.7471 1398.61 2.33264C1490.88 -10.8826 1515.04 33.8763 1701.58 -31.0333" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.727 356.512C401.727 356.512 503.586 326.038 555.903 307.572C608.219 289.106 650.617 229.534 719.275 213.877C783.394 199.254 795.823 221.052 860.133 218.589C952.787 215.04 940.752 54.2268 1021.32 47.4586C1104.77 40.4487 1073.52 195.699 1214.14 146.88C1259.6 131.262 1335.73 1.85922 1401.95 -6.71926C1492.36 -18.4309 1514.95 34.6695 1701.58 -31.0359" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.785 356.706C401.785 356.706 501.667 327.297 553.463 307.087C605.258 286.877 648.865 215.844 719.542 200.366C786.13 185.783 793.04 216.238 859.025 209.565C949.617 200.405 936.078 45.5558 1018.6 37.1757C1101.53 28.7544 1073.64 181.886 1207.71 134.177C1250.92 118.968 1338.59 -8.04478 1405.3 -15.7625C1493.84 -26.0048 1514.86 35.4715 1701.59 -31.0298" stroke="url(#paint1_linear_0_1)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.838 356.888C401.838 356.888 499.743 328.544 551.018 306.59C602.293 284.636 647.108 202.148 719.804 186.843C788.863 172.305 795.394 228.351 857.912 200.53C939.879 164.055 931.413 36.9204 1015.87 26.8816C1098.3 17.0851 1073.76 168.061 1201.27 121.463C1242.23 106.662 1341.44 -17.9872 1408.65 -24.817C1495.3 -33.6229 1514.77 36.2621 1701.58 -31.0348" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.894 357.078C401.894 357.078 497.821 329.8 548.576 306.102C599.33 282.404 645.354 188.464 720.069 173.33C791.599 158.84 793.363 223.379 856.802 191.504C936.417 151.501 926.762 28.3409 1013.15 16.5964C1095.08 5.45763 1073.88 154.246 1194.83 108.758C1233.54 94.3649 1344.29 -27.9507 1412 -33.8628C1496.77 -41.2658 1514.68 37.0613 1701.58 -31.0313" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M401.953 357.267C401.953 357.267 495.902 331.054 546.136 305.612C596.37 280.17 643.604 174.782 720.337 159.814C794.338 145.379 791.446 218.547 855.694 182.476C933.078 139.031 922.13 19.8065 1010.43 6.30894C1091.87 -6.14144 1074 140.428 1188.4 96.0502C1224.86 82.0661 1347.14 -37.9471 1415.35 -42.9106C1498.23 -48.9428 1514.59 37.8587 1701.59 -31.0298" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M402.004 357.448C402.004 357.448 493.976 332.3 543.689 305.114C593.402 277.927 641.846 161.096 720.597 146.29C797.07 131.912 789.64 213.837 854.579 173.439C929.853 126.613 917.504 11.3106 1007.7 -3.98678C1088.67 -17.7208 1074.11 126.602 1181.96 83.3348C1216.17 69.7587 1349.98 -47.986 1418.69 -51.9671C1499.69 -56.6602 1514.5 38.6478 1701.58 -31.0364" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M402.063 357.638C402.063 357.638 492.057 333.556 541.25 304.625C590.443 275.695 640.096 147.423 720.865 132.776C799.811 118.46 787.965 209.258 853.472 164.413C926.758 114.243 912.905 2.87109 1004.97 -14.2729C1085.49 -29.2654 1074.24 112.785 1175.53 70.6288C1207.49 57.4613 1352.82 -58.0511 1422.04 -61.0135C1501.15 -64.3993 1514.41 39.4471 1701.59 -31.0334" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M402.116 357.825C402.116 357.825 490.132 334.807 538.804 304.133C587.476 273.458 638.378 133.919 721.126 119.258C802.582 105.175 786.411 204.78 852.359 155.382C923.777 101.885 908.316 -5.52582 1002.25 -24.5624C1082.32 -40.7907 1074.35 98.9653 1169.09 57.9187C1198.8 45.1595 1355.66 -68.1592 1425.38 -70.0637C1502.61 -72.1729 1514.32 40.2419 1701.58 -31.0344" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M402.171 358.011C402.171 358.011 488.209 336.059 536.361 303.64C584.513 271.222 636.612 120.192 721.39 105.74C805.307 91.6717 784.986 200.394 851.248 146.351C920.919 89.5273 903.748 -13.8767 999.52 -34.8526C1079.16 -52.2957 1074.47 85.1445 1162.65 45.2088C1190.11 32.858 1358.5 -78.3098 1428.73 -79.1143C1504.07 -79.9775 1514.23 41.0366 1701.58 -31.0355" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M402.229 358.2C402.229 358.2 486.29 337.313 533.921 303.15C581.551 268.988 634.849 106.469 721.657 92.2238C808.035 78.1717 783.689 196.087 850.14 137.323C918.179 77.1544 899.202 -22.1795 996.798 -45.1399C1076.02 -63.7787 1074.59 71.327 1156.22 32.5014C1181.43 20.5595 1361.34 -88.5027 1432.08 -88.1619C1505.54 -87.8082 1514.14 41.8341 1701.58 -31.0337" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M402.284 358.388C402.284 358.388 484.367 338.566 531.477 302.66C578.587 266.753 633.083 92.7442 721.921 78.7075C810.76 64.6708 782.511 191.838 849.028 128.294C915.545 64.7502 894.674 -30.4381 994.071 -55.4282C1072.9 -75.2457 1074.71 57.5086 1149.78 19.7931C1172.74 8.25947 1364.18 -98.7449 1435.43 -97.2104C1507.01 -95.6691 1514.05 42.6306 1701.58 -31.033" stroke="white" stroke-width="0.5" stroke-miterlimit="10"/>
</g>
</mask>
<g mask="url(#mask0_0_1)">
<path d="M340.992 -149.016L1403.89 -466.299L1680.12 459.056C1683.28 469.64 1677.26 480.781 1666.68 483.941L642.102 789.783C631.518 792.942 620.377 786.923 617.217 776.339L340.992 -149.016Z" fill="#D9D9D9"/>
</g>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_0_1" x1="0" y1="0" x2="0" y2="288" gradientUnits="userSpaceOnUse">
<stop stop-color="#89101C"/>
<stop offset="1" stop-color="#FF5F68"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="385.581" y1="302.425" x2="1685.31" y2="-85.5531" gradientUnits="userSpaceOnUse">
<stop stop-color="#563CC8"/>
<stop offset="1" stop-color="#D1C6F0"/>
</linearGradient>
<clipPath id="clip0_0_1">
<rect width="905" height="286" fill="white" transform="translate(523)"/>
</clipPath>
<clipPath id="clip1_0_1">
<rect width="1356.51" height="441.571" fill="white" transform="translate(336.867 139.363) rotate(-16.6207)"/>
</clipPath>
</defs>
</svg>
