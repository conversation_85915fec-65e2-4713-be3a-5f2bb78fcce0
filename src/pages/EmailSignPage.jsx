import React, { useEffect } from 'react';
import SEO from '../components/SEO';

const EmailSignPage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="container" style={{ paddingTop: '8rem', paddingBottom: '4rem' }}>
      <SEO 
        title="Email Signature"
        description="ResearchSat email signature template"
        noindex={true} // Don't index this page
      />
      
      <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
        <tbody>
          <tr>
            <td>
              <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                <tbody>
                  <tr>
                    <td style={{ verticalAlign: 'middle' }}>
                      <h2 color="#17242d" className="name__NameContainer-sc-1m457h3-0 jxbGUj" style={{ margin: '0px', fontSize: '18px', color: 'rgb(23, 36, 45)', fontWeight: '600' }}>
                        <span>RAVITEJA</span><span>&nbsp;</span><span>DUGGINENI</span>
                      </h2>
                      <p color="#17242d" fontSize="medium" className="job-title__Container-sc-1hmtp73-0 ifJNJc" style={{ margin: '0px', color: 'rgb(23, 36, 45)', fontSize: '14px', lineHeight: '22px' }}>
                        <span>Founder &amp; CEO</span>
                      </p>
                      <p color="#17242d" fontSize="medium" className="company-details__CompanyContainer-sc-j5pyy8-0 VnOLK" style={{ margin: '0px', fontWeight: '500', color: 'rgb(23, 36, 45)', fontSize: '14px', lineHeight: '22px' }}>
                        <span>ResearchSat</span>
                      </p>
                    </td>
                    <td width="30"><div style={{ width: '30px' }}></div></td>
                    <td color="#ff3340" direction="vertical" width="1" height="auto" className="color-divider__Divider-sc-1h38qjv-0 llIisW" style={{ width: '1px', borderBottom: 'none', borderLeft: '1px solid rgb(255, 51, 64)' }}></td>
                    <td width="30"><div style={{ width: '30px' }}></div></td>
                    <td style={{ verticalAlign: 'middle' }}>
                      <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                        <tbody>
                          <tr height="25" style={{ verticalAlign: 'middle' }}>
                            <td width="30" style={{ verticalAlign: 'middle' }}>
                              <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                                <tbody>
                                  <tr>
                                    <td style={{ verticalAlign: 'bottom' }}>
                                      <span color="#ff3340" width="11" className="contact-info__IconWrapper-sc-mmkjr6-1 bglVXe" style={{ display: 'inline-block', backgroundColor: 'rgb(255, 51, 64)' }}>
                                        <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/phone-icon-2x.png" color="#ff3340" alt="mobilePhone" width="13" className="contact-info__ContactLabelIcon-sc-mmkjr6-0 cnkwri" style={{ display: 'block', backgroundColor: 'rgb(255, 51, 64)' }} />
                                      </span>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td style={{ padding: '0px', color: 'rgb(23, 36, 45)' }}>
                              <a href="tel:0452594883" color="#17242d" className="contact-info__ExternalLink-sc-mmkjr6-2 ibLXSU" style={{ textDecoration: 'none', color: 'rgb(23, 36, 45)', fontSize: '12px' }}>
                                <span>0452594883</span>
                              </a>
                            </td>
                          </tr>
                          <tr height="25" style={{ verticalAlign: 'middle' }}>
                            <td width="30" style={{ verticalAlign: 'middle' }}>
                              <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                                <tbody>
                                  <tr>
                                    <td style={{ verticalAlign: 'bottom' }}>
                                      <span color="#ff3340" width="11" className="contact-info__IconWrapper-sc-mmkjr6-1 bglVXe" style={{ display: 'inline-block', backgroundColor: 'rgb(255, 51, 64)' }}>
                                        <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/email-icon-2x.png" color="#ff3340" alt="emailAddress" width="13" className="contact-info__ContactLabelIcon-sc-mmkjr6-0 cnkwri" style={{ display: 'block', backgroundColor: 'rgb(255, 51, 64)' }} />
                                      </span>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td style={{ padding: '0px' }}>
                              <a href="mailto:<EMAIL>" color="#17242d" className="contact-info__ExternalLink-sc-mmkjr6-2 ibLXSU" style={{ textDecoration: 'none', color: 'rgb(23, 36, 45)', fontSize: '12px' }}>
                                <span><EMAIL></span>
                              </a>
                            </td>
                          </tr>
                          <tr height="25" style={{ verticalAlign: 'middle' }}>
                            <td width="30" style={{ verticalAlign: 'middle' }}>
                              <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                                <tbody>
                                  <tr>
                                    <td style={{ verticalAlign: 'bottom' }}>
                                      <span color="#ff3340" width="11" className="contact-info__IconWrapper-sc-mmkjr6-1 bglVXe" style={{ display: 'inline-block', backgroundColor: 'rgb(255, 51, 64)' }}>
                                        <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/link-icon-2x.png" color="#ff3340" alt="website" width="13" className="contact-info__ContactLabelIcon-sc-mmkjr6-0 cnkwri" style={{ display: 'block', backgroundColor: 'rgb(255, 51, 64)' }} />
                                      </span>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td style={{ padding: '0px' }}>
                              <a href="//www.researchsat.space" color="#17242d" className="contact-info__ExternalLink-sc-mmkjr6-2 ibLXSU" style={{ textDecoration: 'none', color: 'rgb(23, 36, 45)', fontSize: '12px' }}>
                                <span>www.researchsat.space</span>
                              </a>
                            </td>
                          </tr>
                          <tr height="25" style={{ verticalAlign: 'middle' }}>
                            <td width="30" style={{ verticalAlign: 'middle' }}>
                              <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                                <tbody>
                                  <tr>
                                    <td style={{ verticalAlign: 'bottom' }}>
                                      <span color="#ff3340" width="11" className="contact-info__IconWrapper-sc-mmkjr6-1 bglVXe" style={{ display: 'inline-block', backgroundColor: 'rgb(255, 51, 64)' }}>
                                        <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/address-icon-2x.png" color="#ff3340" alt="address" width="13" className="contact-info__ContactLabelIcon-sc-mmkjr6-0 cnkwri" style={{ display: 'block', backgroundColor: 'rgb(255, 51, 64)' }} />
                                      </span>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td style={{ padding: '0px' }}>
                              <span color="#17242d" className="contact-info__Address-sc-mmkjr6-3 jxDmGK" style={{ fontSize: '12px', color: 'rgb(23, 36, 45)' }}>
                                <span>ICC, UniSA, Citywest, Adelaide, SA 5000</span>
                              </span>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td>
              <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ width: '100%', verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                <tbody>
                  <tr>
                    <td height="30"></td>
                  </tr>
                  <tr>
                    <td color="#ff3340" direction="horizontal" width="auto" height="1" className="color-divider__Divider-sc-1h38qjv-0 llIisW" style={{ width: '100%', borderBottom: '1px solid rgb(255, 51, 64)', borderLeft: 'none', display: 'block' }}></td>
                  </tr>
                  <tr>
                    <td height="30"></td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td>
              <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ width: '100%', verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                <tbody>
                  <tr>
                    <td style={{ verticalAlign: 'top' }}>
                      <img src="/src/assets/images/ResearchSatLogo.png" role="presentation" width="130" className="image__StyledImage-sc-hupvqm-0 gYgOut" style={{ display: 'inline-block', maxWidth: '130px' }} />
                    </td>
                    <td style={{ textAlign: 'right', verticalAlign: 'top' }}>
                      <table cellPadding="0" cellSpacing="0" className="table__StyledTable-sc-1avdl6r-0 kAbRZI" style={{ display: 'inline-block', verticalAlign: '-webkit-baseline-middle', fontSize: 'medium', fontFamily: 'Trebuchet MS' }}>
                        <tbody>
                          <tr style={{ textAlign: 'right' }}>
                            <td>
                              <a href="https://www.facebook.com/researchsat/" color="#677d7c" className="social-links__LinkAnchor-sc-py8uhj-2 hBVWAh" style={{ display: 'inline-block', padding: '0px', backgroundColor: 'rgb(103, 125, 124)' }}>
                                <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/facebook-icon-2x.png" alt="facebook" color="#677d7c" height="24" className="social-links__LinkImage-sc-py8uhj-1 hSTSwA" style={{ backgroundColor: 'rgb(103, 125, 124)', maxWidth: '135px', display: 'block' }} />
                              </a>
                            </td>
                            <td width="5"><div></div></td>
                            <td>
                              <a href="https://twitter.com/researchsat/" color="#677d7c" className="social-links__LinkAnchor-sc-py8uhj-2 hBVWAh" style={{ display: 'inline-block', padding: '0px', backgroundColor: 'rgb(103, 125, 124)' }}>
                                <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/twitter-icon-2x.png" alt="twitter" color="#677d7c" height="24" className="social-links__LinkImage-sc-py8uhj-1 hSTSwA" style={{ backgroundColor: 'rgb(103, 125, 124)', maxWidth: '135px', display: 'block' }} />
                              </a>
                            </td>
                            <td width="5"><div></div></td>
                            <td>
                              <a href="https://www.linkedin.com/company/researchsat" color="#677d7c" className="social-links__LinkAnchor-sc-py8uhj-2 hBVWAh" style={{ display: 'inline-block', padding: '0px', backgroundColor: 'rgb(103, 125, 124)' }}>
                                <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/linkedin-icon-2x.png" alt="linkedin" color="#677d7c" height="24" className="social-links__LinkImage-sc-py8uhj-1 hSTSwA" style={{ backgroundColor: 'rgb(103, 125, 124)', maxWidth: '135px', display: 'block' }} />
                              </a>
                            </td>
                            <td width="5"><div></div></td>
                            <td>
                              <a href="https://www.instagram.com/researchsat/" color="#677d7c" className="social-links__LinkAnchor-sc-py8uhj-2 hBVWAh" style={{ display: 'inline-block', padding: '0px', backgroundColor: 'rgb(103, 125, 124)' }}>
                                <img src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/instagram-icon-2x.png" alt="instagram" color="#677d7c" height="24" className="social-links__LinkImage-sc-py8uhj-1 hSTSwA" style={{ backgroundColor: 'rgb(103, 125, 124)', maxWidth: '135px', display: 'block' }} />
                              </a>
                            </td>
                            <td width="5"><div></div></td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default EmailSignPage;
