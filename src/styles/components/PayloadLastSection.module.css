.container {
  background: #191a1d;
  padding: 80px;
  display: flex;
  flex-direction: row;
  gap: 351px;
  row-gap: 24px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: center;
  position: relative;
}

.titleContainer {
  width: 300px;
}

.title {
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  margin: 0;
}

.titleText {
  color: #ffffff;
}

.titleHighlight {
  color: #ef3b47;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  min-width: 285px;
}

.description {
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  line-height: 130%;
  letter-spacing: 0.5px;
  font-weight: 400;
  width: 100%;
}

.descriptionText {
  color: #8a8a8a;
}

.descriptionHighlight {
  color: #ef3b47;
}

.descriptionCallout {
  color: #cfd4d5;
}

.button {
  border-radius: 8px;
  border: 1px solid #ffffff;
  padding: 14px 28px;
  display: flex;
  flex-direction: row;
  gap: 7.5px;
  align-items: center;
  justify-content: center;
  width: 229px;
  height: 48px;
  min-width: 120px;
  position: relative;
  color: #ffffff;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.1px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.button:hover {
  background-color: #ffffff;
  color: #191a1d;
}

/* Media Queries */
@media (max-width: 1200px) {
  .container {
    gap: 60px;
    padding: 60px 40px;
  }
}

@media (max-width: 992px) {
  .container {
    flex-direction: column;
    align-items: flex-start;
    gap: 40px;
    padding: 50px 30px;
  }
  
  .titleContainer {
    width: 100%;
  }
  
  .contentContainer {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 40px 20px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .description {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 30px 15px;
  }
  
  .button {
    width: 100%;
  }
}
