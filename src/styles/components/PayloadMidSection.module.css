.container {
  position: relative;
  width: 100%;
  height: 286px;
  margin: 60px 0;
  display: flex;
  justify-content: center;
}

.contentContainer {
  background: linear-gradient(
    180deg,
    rgba(187, 222, 208, 1) 0%,
    rgba(135, 194, 170, 1) 100%
  );
  padding: 80px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1280px;
  position: relative;
  z-index: 1;
}

.textContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 626px;
  position: relative;
}

.description {
  color: #323232;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  position: relative;
  width: 100%;
}

.waveBg {
  background: linear-gradient(
    180deg,
    rgba(187, 222, 208, 1) 0%,
    rgba(135, 194, 170, 1) 100%
  );
  border-radius: 40px;
  width: 696px;
  height: 286px;
  position: absolute;
  right: 0;
  top: 0;
  overflow: hidden;
  z-index: 0;
}

.maskGroup {
  opacity: 0.4;
  height: auto;
  position: absolute;
  left: -186px;
  top: 139px;
  transform: translate(186px, -139px);
  overflow: visible;
}

/* Media Queries */
@media (max-width: 1280px) {
  .contentContainer {
    max-width: 100%;
    padding: 60px 20px;
  }
  
  .waveBg {
    width: 50%;
    right: 0;
  }
}

@media (max-width: 991px) {
  .container {
    height: auto;
  }
  
  .contentContainer {
    padding: 50px 20px;
  }
  
  .textContainer {
    max-width: 100%;
  }
  
  .description {
    font-size: 20px;
  }
  
  .waveBg {
    display: none;
  }
}

@media (max-width: 768px) {
  .contentContainer {
    padding: 40px 20px;
  }
  
  .description {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .contentContainer {
    padding: 30px 15px;
  }
  
  .description {
    font-size: 16px;
  }
}
