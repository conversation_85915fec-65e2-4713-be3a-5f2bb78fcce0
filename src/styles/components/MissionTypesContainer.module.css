.missionTypesContainer {
  background-color: #000000;
  padding: 60px 0;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.headerContainer {
  max-width: 1280px;
  margin: 0 auto 60px;
  padding: 0 20px;
  text-align: center;
}

.mainTitle {
  color: #ffffff;
  font-size: 36px;
  line-height: 1.3;
  font-weight: 500;
  margin-bottom: 20px;
  font-family: 'Poppins', sans-serif;
}

.mainDescription {
  color: #cfd4d5;
  font-size: 18px;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Poppins', sans-serif;
}

/* Media Queries */
@media (max-width: 991px) {
  .missionTypesContainer {
    padding: 40px 0;
  }
  
  .headerContainer {
    margin-bottom: 40px;
  }
  
  .mainTitle {
    font-size: 32px;
  }
  
  .mainDescription {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .missionTypesContainer {
    padding: 30px 0;
  }
  
  .headerContainer {
    margin-bottom: 30px;
  }
  
  .mainTitle {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .missionTypesContainer {
    padding: 20px 0;
  }
  
  .headerContainer {
    padding: 0 15px;
    margin-bottom: 20px;
  }
  
  .mainTitle {
    font-size: 24px;
  }
  
  .mainDescription {
    font-size: 14px;
  }
}
