.heroSection {
  position: relative;
  height: 780px;
  background-color: #001718;
  overflow: hidden;
}

.heroBackground {
  width: 100%;
  height: 462px;
  object-fit: cover;
  position: absolute;
  left: 0;
  top: 0;
}

.gradientOverlay {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 462px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 23, 24, 1) 100%);
}

.topContainer {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  justify-content: flex-end;
  width: 1280px;
  max-width: 100%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 267px;
  padding: 0 20px;
}

.heroContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-end;
  width: 100%;
}

.payloadsLabel {
  color: #ef3b47;
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  line-height: 140%;
  letter-spacing: 0.4px;
  font-weight: 400;
}

.heroHeading {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 47px;
  line-height: 130%;
  font-weight: 500;
  max-width: 955px;
  margin: 0;
}

.bottomContainer {
  margin-top: 60px;
  display: flex;
  flex-direction: row;
  gap: 351px;
  align-items: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 1280px;
  max-width: 100%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 462px;
  padding: 40px 20px;
}

.leftColumn {
  width: 300px;
  flex-shrink: 0;
}

.subHeading {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  margin: 0;
}

.rightColumn {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: flex-start;
  justify-content: center;
  flex: 1;
}

.description {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  line-height: 130%;
  letter-spacing: 0.5px;
  font-weight: 400;
  margin: 0;
}

.grayText {
  color: #8a8a8a;
}

.highlightText {
  color: #ef3b47;
}

.contactButton {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #ffffff;
  padding: 14px 28px;
  display: flex;
  flex-direction: row;
  gap: 7.5px;
  align-items: center;
  justify-content: center;
  width: 229px;
  height: 48px;
  min-width: 120px;
  color: #323232;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.1px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.contactButton:hover {
  background-color: #ff3241;
  border-color: #ff3241;
  color: #ffffff;
}

/* Media Queries */
@media (max-width: 1300px) {
  .topContainer, .bottomContainer {
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
  }

  .bottomContainer {
    gap: 40px;
  }
}

@media (max-width: 991px) {
  .heroSection {
    height: auto;
    padding-bottom: 40px;
  }

  .heroBackground {
    height: 400px;
  }

  .topContainer {
    position: relative;
    top: 200px;
    margin-bottom: 240px;
  }

  .heroHeading {
    font-size: 36px;
    max-width: 100%;
  }

  .bottomContainer {
    position: relative;
    top: 200px;
    flex-direction: column;
    gap: 30px;
  }

  .leftColumn {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .heroBackground {
    height: 300px;
  }

  .topContainer {
    top: 150px;
    margin-bottom: 180px;
  }

  .heroHeading {
    font-size: 28px;
  }

  .subHeading {
    font-size: 20px;
  }

  .description {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .heroBackground {
    height: 250px;
  }

  .topContainer {
    top: 120px;
    margin-bottom: 150px;
  }

  .heroHeading {
    font-size: 24px;
  }

  .contactButton {
    width: 100%;
  }
}
