.gallery {
  width: 100%;
  margin-bottom: 2rem;
}

.galleryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.galleryItem {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  cursor: pointer;
  aspect-ratio: 1 / 1;
  transition: transform 0.3s ease;
}

.galleryItem:hover {
  transform: scale(1.02);
}

.galleryItem:focus {
  outline: 3px solid var(--primary-color, #0E7369);
  outline-offset: 3px;
}

.galleryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.galleryItem:hover .galleryImage {
  transform: scale(1.05);
}

.caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem;
  font-size: 0.875rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.galleryItem:hover .caption {
  opacity: 1;
}

/* Lightbox styles */
.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.lightboxContent {
  position: relative;
  width: 90%;
  height: 90%;
  max-width: 1200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightboxImageContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.lightboxImage {
  max-width: 100%;
  max-height: 85%;
  object-fit: contain;
  animation: zoomIn 0.3s ease;
}

.lightboxCaption {
  color: white;
  text-align: center;
  padding: 1rem;
  max-width: 80%;
  margin-top: 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0.5rem;
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 10;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  transition: background-color 0.3s ease;
}

.closeButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 10;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  transition: background-color 0.3s ease;
}

.navButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.prevButton {
  left: 1rem;
}

.nextButton {
  right: 1rem;
}

.counter {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .galleryGrid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
  }
  
  .navButton {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .closeButton {
    font-size: 1.25rem;
    width: 2rem;
    height: 2rem;
  }
  
  .lightboxCaption {
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
  }
}
