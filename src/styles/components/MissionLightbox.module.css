.lightboxOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow-y: auto;
  padding: 20px;
}

.lightboxContainer {
  background-color: #17242d;
  width: 100%;
  max-width: 1000px;
  position: relative;
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 28px;
  cursor: pointer;
  z-index: 10;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.closeButton:hover {
  color: #ff3241;
}

.lightboxContent {
  display: flex;
  flex-direction: row;
  border-radius: 16px;
  overflow: hidden;
}

.imageSection {
  flex: 1;
  max-width: 50%;
}

.missionImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px 0 0 16px;
}

.contentSection {
  flex: 1;
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.headerContainer {
  margin-top: 20px;
  margin-bottom: 10px;
}

.missionLabel {
  color: #8a8a8a;
  font-size: 16px;
  line-height: 135%;
  letter-spacing: 0.15px;
  font-weight: 400;
  margin-bottom: 10px;
  font-family: 'Poppins', sans-serif;
}

.missionTitle {
  color: #f9f4fb;
  font-size: 32px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  margin-bottom: 10px;
  font-family: 'Poppins', sans-serif;
}

.missionSubtitle {
  color: #5f7371;
  font-size: 16px;
  line-height: 135%;
  font-weight: 400;
  margin-bottom: 10px;
  font-family: 'Poppins', sans-serif;
}

.divider {
  border: 0;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 15px 0;
}

.featuresContainer {
  margin-bottom: 20px;
}

.featuresTitle {
  color: #ffffff;
  font-size: 18px;
  line-height: 135%;
  font-weight: 500;
  margin-bottom: 15px;
  font-family: 'Poppins', sans-serif;
}

.featuresList {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.checkIcon {
  display: inline-block;
  width: 20px;
  height: 20px;
  position: relative;
  flex-shrink: 0;
}

.checkIcon::before {
  content: '✓';
  color: #ff3241;
  font-size: 16px;
  position: absolute;
  top: 0;
  left: 0;
}

.featureText {
  color: #cfd4d5;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 200;
  font-family: 'Poppins', sans-serif;
}

.applicationsContainer {
  margin-bottom: 20px;
}

.applicationsTitle {
  color: #ffffff;
  font-size: 18px;
  line-height: 135%;
  font-weight: 500;
  margin-bottom: 10px;
  font-family: 'Poppins', sans-serif;
}

.applicationsText {
  color: #cfd4d5;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 200;
  font-family: 'Poppins', sans-serif;
}

.buttonsContainer {
  display: flex;
  gap: 15px;
  margin-top: 10px;
  justify-content: flex-start;
}

.contactButton {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 16px 32px;
  gap: 7.5px;
  min-width: 120px;
  height: 51px;
  border: 1px solid #ff3241;
  border-radius: 8px;
  background: #ff3241;
  font-family: 'Poppins', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  color: #fffef6;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
}

.contactButton:hover {
  background: #fffef6;
  border-color: #fffef6;
  color: #17242d;
}

.backButton {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 16px 32px;
  gap: 7.5px;
  min-width: 120px;
  height: 51px;
  background: transparent;
  border: 1px solid #fffef6;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  color: #fffef6;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
}

.backButton:hover {
  background: #ffffff;
  border-color: #ffffff;
  color: #17242d;
}

/* Media Queries */
@media (max-width: 991px) {
  .lightboxContent {
    flex-direction: column;
  }

  .imageSection {
    max-width: 100%;
    height: 300px;
  }

  .missionTitle {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .lightboxContainer {
    max-width: 90%;
  }

  .contentSection {
    padding: 20px;
  }

  .missionTitle {
    font-size: 24px;
  }

  .buttonsContainer {
    flex-direction: column;
  }

  .contactButton, .backButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .lightboxContainer {
    max-width: 95%;
  }

  .contentSection {
    padding: 15px;
  }

  .missionTitle {
    font-size: 20px;
  }

  .missionSubtitle {
    font-size: 14px;
  }

  .featuresTitle, .applicationsTitle {
    font-size: 16px;
  }

  .featureText, .applicationsText {
    font-size: 14px;
  }
}
