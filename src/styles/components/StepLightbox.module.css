/* StepLightbox.module.css */
.lightboxOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow-y: auto;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.lightboxContainer {
  background-color: #17242d;
  width: 100%;
  max-width: 1200px;
  position: relative;
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  overflow: hidden;
  animation: scaleIn 0.3s ease;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  color: #ffffff;
  font-size: 28px;
  cursor: pointer;
  z-index: 10;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 300;
}

.closeButton:hover {
  background-color: rgba(255, 50, 65, 0.8);
  transform: scale(1.1);
}

.lightboxContent {
  display: flex;
  flex-direction: row;
  border-radius: 24px;
  overflow: hidden;
  min-height: 600px;
}

.imageSection {
  flex: 0 0 45%;
  position: relative;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.stepImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.8;
}

.stepBadge {
  position: absolute;
  top: 24px;
  left: 24px;
  background: linear-gradient(135deg, #87C2AA 0%, #BBDED0 100%);
  border-radius: 16px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 16px rgba(135, 194, 170, 0.3);
}

.stepNumber {
  color: #1a1a1a;
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 700;
  line-height: 1;
}

.stepIcon {
  font-size: 20px;
  line-height: 1;
}

.contentSection {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  max-height: 80vh;
}

.headerContainer {
  margin-bottom: 8px;
}

.stepTitle {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 0 12px 0;
  background: linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stepSubtitle {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 400;
  line-height: 140%;
  margin: 0;
}

.divider {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(135, 194, 170, 0.5) 50%, transparent 100%);
  margin: 16px 0;
}

.detailsContainer {
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.sectionTitle {
  color: #BBDED0;
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 0 16px 0;
}

.overviewText {
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 160%;
  margin: 0;
}

.activitiesList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activityItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  line-height: 150%;
}

.activityIcon {
  color: #87C2AA;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
}

.deliverablesList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deliverableItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #87C2AA;
}

.deliverableIcon {
  font-size: 16px;
  flex-shrink: 0;
}

.deliverableText {
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  line-height: 140%;
}

.timelineInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timelineItem {
  display: flex;
  align-items: center;
  gap: 12px;
}

.timelineIcon {
  font-size: 16px;
  flex-shrink: 0;
}

.timelineText {
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  line-height: 140%;
}

.buttonsContainer {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.consultationButton {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 16px 32px;
  gap: 8px;
  min-width: 200px;
  height: 52px;
  background: linear-gradient(135deg, #87C2AA 0%, #BBDED0 100%);
  border: none;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  color: #1a1a1a;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.consultationButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(135, 194, 170, 0.4);
}

.backButton {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 16px 32px;
  gap: 8px;
  min-width: 160px;
  height: 52px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 991px) {
  .lightboxContent {
    flex-direction: column;
    min-height: auto;
  }

  .imageSection {
    flex: none;
    height: 300px;
  }

  .contentSection {
    max-height: none;
    padding: 30px;
  }

  .stepTitle {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .lightboxOverlay {
    padding: 10px;
  }

  .lightboxContainer {
    border-radius: 16px;
  }

  .contentSection {
    padding: 24px;
    gap: 20px;
  }

  .stepTitle {
    font-size: 24px;
  }

  .buttonsContainer {
    flex-direction: column;
  }

  .consultationButton,
  .backButton {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .contentSection {
    padding: 20px;
  }

  .stepTitle {
    font-size: 22px;
  }

  .stepSubtitle {
    font-size: 16px;
  }

  .sectionTitle {
    font-size: 18px;
  }
}
