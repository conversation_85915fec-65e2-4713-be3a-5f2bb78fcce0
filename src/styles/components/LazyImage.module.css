/* LazyImage.module.css */
.lazyImageContainer {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-color: #e2e2e2;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: filter 0.3s ease-in-out;
}

/* Maintain aspect ratio for responsive images */
.lazyImageContainer[style*="aspect-ratio"] {
  height: auto;
}

/* Blur effect for images while loading */
.blur {
  filter: blur(10px);
}

/* Remove blur when image is loaded */
.lazyImageContainer[data-loaded="true"] {
  filter: blur(0);
}

.lazyImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  display: block;
  max-width: 100%;
}

.loaded {
  opacity: 1;
}

/* Picture element should fill the container */
.lazyImageContainer picture {
  width: 100%;
  height: 100%;
  display: block;
}

.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: inherit;
  z-index: 1;
  transition: opacity 0.3s ease-in-out;
}

/* Hide placeholder when image is loaded */
.lazyImageContainer[data-loaded="true"] .placeholder {
  opacity: 0;
  pointer-events: none;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #0E7369;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .spinner {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 2px;
  }
}

/* Accessibility improvements */
.lazyImageContainer:focus-within {
  outline: 2px solid #0E7369;
  outline-offset: 2px;
}
