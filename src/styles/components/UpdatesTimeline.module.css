/* UpdatesTimeline.module.css */

.timelineContainer {
  padding: 80px 0;
  background-color: #080809;
  color: #f9f4fb;
}

.timelineHeader {
  text-align: center;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 20px;
}

.timelineTitle {
  font-size: 36px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 20px;
  color: #f9f4fb;
}

.timelineSubtitle {
  font-size: 18px;
  line-height: 160%;
  color: #e0e0e0;
  margin-bottom: 30px;
}

.timelineYearSelector {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 30px;
}

.yearButton {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: #8a8a8a;
  padding: 8px 20px;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.yearButton:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: #f9f4fb;
}

.yearButton.activeYear {
  background-color: #ff3241;
  color: #ffffff;
}

.timeline {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 40px;
}

.timelineLine {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 20%;
  width: 2px;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(-50%);
}

.timelineItem {
  position: relative;
  margin-bottom: 60px;
  display: flex;
  justify-content: flex-start;
}

.timelinePoint {
  position: absolute;
  left: 17.5%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #ff3241;
  z-index: 2;
  box-shadow: 0 0 0 4px rgba(255, 50, 65, 0.3);
}

.timelineDate {
  position: absolute;
  left: 20%;
  transform: translateX(-170%);
  width: 100px;
  text-align: right;
  color: #8a8a8a;
  font-size: 14px;
  font-weight: 500;
  padding-top: 2px;
}

.timelineContent {
  width: 65%;
  background-color: #1e1e1e;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  margin-left: calc(20% + 20px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timelineContent:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.timelineContent::before {
  content: '';
  position: absolute;
  top: 20px;
  left: -20px;
  border: 10px solid transparent;
  border-right-color: #1e1e1e;
}

.timelineMeta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  align-items: center;
}

/* Removed as we moved this to a different position */

.timelineCategory {
  color: #ff3241;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: rgba(255, 50, 65, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.timelineItemTitle {
  color: #f9f4fb;
  font-size: 20px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 16px;
}

.timelineItemContent {
  color: #e0e0e0;
  font-size: 16px;
  line-height: 160%;
  margin-bottom: 20px;
}

.timelineLinks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timelineLinksLabel {
  color: #8a8a8a;
  font-size: 14px;
  font-weight: 500;
}

.timelineLinksList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.timelineLink {
  color: #ff3241;
  font-size: 14px;
  text-decoration: none;
  background-color: rgba(255, 50, 65, 0.1);
  padding: 4px 10px;
  border-radius: 4px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.timelineLink:hover {
  background-color: rgba(255, 50, 65, 0.2);
  transform: translateY(-2px);
}

.timelineEnd {
  text-align: center;
  padding: 20px 0;
  position: relative;
  z-index: 2;
}

.timelineEndLabel {
  background-color: #1e1e1e;
  color: #8a8a8a;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
}

/* Media Queries */
@media (max-width: 992px) {
  .timelineTitle {
    font-size: 32px;
  }

  .timelineSubtitle {
    font-size: 16px;
  }

  .yearButton {
    padding: 6px 16px;
    font-size: 14px;
  }

  .timelineLine {
    left: 60px;
  }

  .timelinePoint {
    left: 60px;
  }

  .timelineDate {
    left: 60px;
    transform: translateX(-170%);
    width: 90px;
    font-size: 13px;
  }

  .timelineContent {
    width: calc(100% - 100px);
    margin-left: 80px;
  }
}

@media (max-width: 768px) {
  .timelineYearSelector {
    gap: 10px;
  }

  .yearButton {
    padding: 5px 12px;
    font-size: 13px;
  }

  .timelineLine {
    left: 40px;
  }

  .timelinePoint {
    left: 40px;
  }

  .timelineDate {
    left: 40px;
    transform: translateX(-150%);
    width: 80px;
    font-size: 12px;
  }

  .timelineContent {
    width: calc(100% - 60px);
    margin-left: 60px;
  }

  .timelineContent::before {
    left: -20px;
    border-right-color: #1e1e1e;
  }
}

@media (max-width: 576px) {
  .timelineContainer {
    padding: 60px 0;
  }

  .timelineHeader {
    margin-bottom: 40px;
  }

  .timelineTitle {
    font-size: 28px;
  }

  .timelineYearSelector {
    flex-wrap: wrap;
    gap: 8px;
  }

  .yearButton {
    padding: 4px 10px;
    font-size: 12px;
    flex: 1;
    min-width: 80px;
  }

  .timeline {
    padding: 0 20px;
  }

  .timelineLine {
    left: 20px;
  }

  .timelinePoint {
    left: 20px;
    width: 16px;
    height: 16px;
  }

  .timelineDate {
    position: relative;
    left: 0;
    transform: none;
    width: auto;
    text-align: left;
    margin-bottom: 8px;
    margin-left: 36px;
    font-weight: 600;
    color: #ff3241;
  }

  .timelineContent {
    width: calc(100% - 30px);
    margin-left: 30px;
    padding: 16px;
  }

  .timelineItemTitle {
    font-size: 18px;
  }

  .timelineItemContent {
    font-size: 14px;
  }
}
