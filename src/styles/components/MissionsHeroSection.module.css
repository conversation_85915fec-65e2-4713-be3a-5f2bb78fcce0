.heroSection {
  position: relative;
  height: 462px;
  width: 100%;
  overflow: hidden;
  margin-bottom: 60px;
}

.heroImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  left: 0;
  top: 0;
}

.container {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  justify-content: flex-end;
  max-width: 1280px;
  width: 100%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 267px;
  padding: 0 20px;
  z-index: 3;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-end;
  width: 100%;
}

.label {
  color: #ef3b47;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  line-height: 140%;
  letter-spacing: 0.4px;
  font-weight: 400;
}

.title {
  color: #ffffff;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 47px;
  line-height: 130%;
  font-weight: 500;
  max-width: 955px;
}

/* Add a gradient overlay to improve text readability */
.heroSection::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.6));
  z-index: 1;
}

/* Gradient overlay to blend into next section */
.heroSection::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 150px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), #000000);
  z-index: 2;
}

/* Media Queries */
@media (max-width: 1200px) {
  .container {
    max-width: 90%;
  }

  .title {
    font-size: 40px;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .heroSection {
    height: 400px;
  }

  .container {
    top: 220px;
  }

  .title {
    font-size: 32px;
    max-width: 600px;
  }
}

@media (max-width: 480px) {
  .heroSection {
    height: 350px;
  }

  .container {
    top: 180px;
  }

  .title {
    font-size: 24px;
    max-width: 100%;
  }
}
