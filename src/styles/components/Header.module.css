/* Header.module.css */
.header {
  position: relative;
  height: 889px;
  width: 100%;
  overflow: hidden;
  background-color: #000000;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
}

.heroContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Background Image is now handled by BackgroundImage component */

/* Right Side Content */
.rightContent {
  position: absolute;
  right: 80px;
  top: 136px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  z-index: 10;
}

.brandText {
  display: flex;
  flex-direction: column;
  text-align: right;
  width: 188px;
  margin-bottom: 30px;
}

.brandName {
  color: #ff3241;
  font-family: 'Poppins', sans-serif;
  font-weight: 300;
  font-size: 16px;
  line-height: 130%;
  letter-spacing: 0.5px;
  margin-bottom: 5px;
}

.tagline {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-weight: 300;
  font-size: 16px;
  line-height: 130%;
  letter-spacing: 0.5px;
}

.buttonWrapper {
  margin-top: 10px;
}

.exploreButton {
  border-radius: 8px;
  border: 1px solid #ffffff;
  padding: 16px 32px;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
  letter-spacing: 0.25px;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  width: 229px;
  text-align: center;
}

.exploreButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Mobile Text Container - Hidden by default */
.mobileTextContainer {
  display: none;
}

/* Bottom Content - Merged for easier handling */
.bottomContent {
  position: absolute;
  left: 80px;
  top: 50vh;
  z-index: 10;
  color: #ffffff;
  font-family: 'Exo 2', sans-serif;
  font-size: 64px;
  line-height: 95%;
  letter-spacing: -0.5px;
  font-weight: 400;
}

/* Glow effect using pseudo-element for cleaner structure */
.bottomContent::before {
  content: '';
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(19, 19, 20, 0.8) 100%
  );
  width: 370px;
  height: 70px;
  position: absolute;
  left: -13px;
  bottom: 150px;
  filter: blur(125px);
  z-index: -1;
}

/* Unified text styling - all text in one container */
.bottomContent .mainText span {
  display: inline-block;
  margin-right: 10px;
}

/* Remove margin from the last span on each line */
.bottomContent .mainText span:last-child {
  margin-right: 0;
}

/* Media Queries */
@media (max-width: 1200px) {
  .header {
    height: 800px;
  }

  .bottomContent {
    font-size: 56px;
  }
}

@media (max-width: 992px) {
  .header {
    height: 700px;
  }

  .rightContent {
    right: 40px;
    top: 120px;
  }

  .bottomContent {
    left: 40px;
    bottom: 10vh;
    font-size: 48px;
  }

  .bottomContent::before {
    width: 300px;
    height: 60px;
    bottom: 120px;
  }
}

@media (max-width: 768px) {
  .header {
    height: 600px;
  }

  /* Tablet background image positioning */
  .heroContainer {
    background-position: center center;
    background-size: cover;
  }

  .rightContent {
    right: 20px;
    top: 100px;
  }

  .bottomContent {
    left: 20px;
    bottom: 10vh;
    font-size: 40px;
  }

  .bottomContent::before {
    width: 250px;
    height: 50px;
    bottom: 100px;
  }

  .exploreButton {
    padding: 12px 24px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .header {
    height: 75vh;
  }

  /* Mobile background image positioning */
  .heroContainer {
    background-position: center center !important;
    background-size: cover !important;
  }

  /* Mobile container for all text content */
  .mobileTextContainer {
    display: flex;
    width: 343px;
    height: 467px;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    flex-shrink: 0;
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }

  /* Mobile positioning for bottomContent */
  .bottomContent {
    display: block;
    position: absolute;
    left: 10vw;
    bottom: auto;
    font-size: 32px;
    top: -10vh;
  }

  /* Mobile positioning for rightContent */
  .rightContent {
    position: absolute;
    right: 10vw;
    top: 50vh;
    width: auto;
  }

  /* Mobile glow effect positioning */
  .bottomContent::before {
    width: 200px;
    height: 40px;
    bottom: 80px;
  }

  /* Mobile text sizing for repositioned elements */
  .bottomContent .mainText span {
    font-size: 42px;
  }

  .rightContent .brandText {
    width: 160px;
  }

  .rightContent .brandName,
  .rightContent .tagline {
    font-size: 14px;
  }

  .rightContent .exploreButton {
    padding: 10px 20px;
    font-size: 12px;
    width: auto;
  }

  /* Mobile-specific styles for text elements */
  .mobileMainHeading {
    color: #ffffff;
    font-family: 'Poppins', sans-serif;
    font-size: 36px;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 8px;
  }

  .mobileDescription {
    color: #ffffff;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.4;
    text-align: left;
    margin-bottom: 32px;
  }

  .mobileBrandName {
    color: #ff3241;
    font-weight: 500;
  }

  .mobileExploreButton {
    border-radius: 24px;
    border: 1px solid #ffffff;
    background: transparent;
    padding: 16px 32px;
    color: #ffffff;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
  }

  .mobileExploreButton:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
}
