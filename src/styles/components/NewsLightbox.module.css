.lightboxOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow-y: auto;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.lightboxContainer {
  background-color: #17242d;
  width: 100%;
  max-width: 1000px;
  position: relative;
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  animation: scaleIn 0.3s ease;
}

.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: #ffffff;
  font-size: 28px;
  cursor: pointer;
  z-index: 10;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.closeButton:hover {
  background-color: rgba(255, 50, 65, 0.8);
}

.lightboxContent {
  display: flex;
  flex-direction: row;
  border-radius: 16px;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

.lightboxContent.animating {
  opacity: 0.5;
}

.imageSection {
  flex: 0 0 40%;
  position: relative;
}

.cardImage {
  width: 100%;
  height: 100%;
  min-height: 500px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.cardImage::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
}

.cardCategory {
  position: absolute;
  top: 24px;
  left: 24px;
  background-color: rgba(255, 50, 65, 0.9);
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  z-index: 2;
}

.contentSection {
  flex: 0 0 60%;
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: #121212;
  color: #f9f4fb;
}

.headerContainer {
  margin-bottom: 8px;
}

.cardTitle {
  color: #f9f4fb;
  font-size: 28px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 12px;
}

.cardDate {
  color: #8a8a8a;
  font-size: 14px;
  font-weight: 400;
}

.authorContainer {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.authorImage {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #687e7c;
}

.authorImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.authorName {
  color: #8a8a8a;
  font-size: 16px;
  font-weight: 500;
}

.cardContent {
  color: #e0e0e0;
  font-size: 16px;
  line-height: 160%;
  margin-bottom: 24px;
  flex: 1;
  overflow-y: auto;
  max-height: 200px;
  padding-right: 10px;
}

.cardContent p {
  margin-bottom: 16px;
}

.cardContent p:last-child {
  margin-bottom: 0;
}

/* Custom scrollbar for content */
.cardContent::-webkit-scrollbar {
  width: 6px;
}

.cardContent::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.cardContent::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.cardContent::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.buttonsContainer {
  display: flex;
  justify-content: center;
  margin-top: auto;
}

.backButton {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 24px;
  background-color: #ff3241;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
  min-width: 120px;
}

.backButton:hover {
  background-color: #e62b39;
  transform: translateY(-2px);
}

/* Navigation Arrows */
.navArrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

.prevArrow {
  left: 16px;
}

.nextArrow {
  right: 16px;
}

.navArrow:hover {
  background-color: rgba(255, 50, 65, 0.8);
  transform: translateY(-50%) scale(1.05);
}

.navArrow:active {
  transform: translateY(-50%) scale(0.95);
}

.navArrow img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

/* Pagination Indicators */
.paginationIndicators {
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 8px;
  z-index: 10;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
}

.indicator.active {
  background-color: #ffffff;
  transform: scale(1.2);
}

.indicator:hover {
  background-color: rgba(255, 255, 255, 0.5);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
  }
  to {
    transform: scale(1);
  }
}

/* Media Queries */
@media (max-width: 992px) {
  .lightboxContent {
    flex-direction: column;
  }

  .imageSection {
    flex: 0 0 auto;
  }

  .cardImage {
    min-height: 300px;
  }

  .contentSection {
    flex: 1;
    padding: 30px;
  }

  .cardTitle {
    font-size: 24px;
  }

  .cardContent {
    max-height: 150px;
  }
}

@media (max-width: 768px) {
  .lightboxContainer {
    max-width: 90%;
  }

  .contentSection {
    padding: 24px;
  }

  .cardTitle {
    font-size: 22px;
  }

  .buttonsContainer {
    flex-direction: column;
  }

  .navArrow {
    width: 40px;
    height: 40px;
  }

  .navArrow img {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 576px) {
  .lightboxContainer {
    max-width: 95%;
  }

  .contentSection {
    padding: 20px;
  }

  .cardTitle {
    font-size: 20px;
  }

  .cardContent {
    font-size: 14px;
    max-height: 120px;
  }

  .navArrow {
    width: 36px;
    height: 36px;
  }

  .navArrow img {
    width: 18px;
    height: 18px;
  }
}
