/* LoadingFallback.module.css */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  position: relative;
  margin-bottom: 1rem;
}

.doubleBounce1, .doubleBounce2 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #0E7369;
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  animation: bounce 2s infinite ease-in-out;
}

.doubleBounce2 {
  animation-delay: -1.0s;
}

.loadingText {
  color: #0E7369;
  font-weight: 600;
  font-size: 1rem;
}

@keyframes bounce {
  0%, 100% { 
    transform: scale(0.0);
  } 
  50% { 
    transform: scale(1.0);
  }
}
