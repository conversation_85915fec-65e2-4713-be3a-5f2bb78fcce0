.missionSection {
  padding: 60px 0;
  background-color: #000000;
  font-family: 'Poppins', sans-serif;
  color: #ffffff;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-direction: row;
  gap: 40px;
  align-items: center;
}

.imageLeft .container {
  flex-direction: row-reverse;
}

.contentContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
}

.missionTitle {
  color: #ffffff;
  font-size: 28px;
  line-height: 1.3;
  font-weight: 500;
  margin-bottom: 10px;
}

.missionSubtitle {
  color: #5f7371;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  margin-bottom: 15px;
}

.missionDescription {
  color: #cfd4d5;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.featuresList {
  list-style-type: none;
  padding: 0;
  margin: 0 0 20px 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.checkIcon {
  display: inline-block;
  width: 20px;
  height: 20px;
  position: relative;
  flex-shrink: 0;
}

.checkIcon::before {
  content: '✓';
  color: #ff3241;
  font-size: 16px;
  position: absolute;
  top: 0;
  left: 0;
}

.featureText {
  color: #cfd4d5;
  font-size: 16px;
  line-height: 1.5;
}

.applicationsText {
  color: #cfd4d5;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.applicationsTitle {
  color: #ff3241;
  font-weight: 600;
  margin-right: 5px;
}

.learnMoreButton {
  display: inline-block;
  background-color: #ff3241;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 4px;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.learnMoreButton:hover {
  background-color: #107e7d;
}

.imageContainer {
  flex: 1;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
}

.missionImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.imageContainer:hover .missionImage {
  transform: scale(1.05);
}

/* Media Queries */
@media (max-width: 991px) {
  .missionSection {
    padding: 40px 0;
  }
  
  .container {
    gap: 30px;
  }
  
  .missionTitle {
    font-size: 24px;
  }
  
  .imageContainer {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .container, .imageLeft .container {
    flex-direction: column;
  }
  
  .contentContainer {
    width: 100%;
  }
  
  .imageContainer {
    width: 100%;
    height: 300px;
  }
  
  .missionTitle {
    font-size: 22px;
  }
  
  .missionSubtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .missionSection {
    padding: 30px 0;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .imageContainer {
    height: 250px;
  }
  
  .missionTitle {
    font-size: 20px;
  }
  
  .missionDescription, .featureText, .applicationsText {
    font-size: 14px;
  }
  
  .learnMoreButton {
    font-size: 14px;
    padding: 10px 20px;
  }
}
