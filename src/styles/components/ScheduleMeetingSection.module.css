.scheduleMeetingSection {
  width: 100%;
  margin: 60px 0;
  position: relative;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  position: relative;
  height: 360px;
}

.backgroundImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  position: absolute;
  left: 0;
  top: 0;
  filter: brightness(0.8);
}

.contentWrapper {
  padding: 24px 40px;
  display: flex;
  flex-direction: row;
  gap: 22px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: absolute;
  left: 0;
  top: 84px;
}

.leftContent {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 629px;
  min-width: 285px;
}

.messageContainer {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  line-height: 130%;
  letter-spacing: 0.5px;
  font-weight: 400;
  width: 100%;
}

.grayText {
  color: #8a8a8a;
}

.whiteText {
  color: #e6e6e6;
}

.scheduleButton {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #ffffff;
  padding: 14px 28px;
  display: flex;
  flex-direction: row;
  gap: 7.5px;
  align-items: center;
  justify-content: center;
  width: 229px;
  height: 48px;
  min-width: 120px;
  color: #323232;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.1px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.scheduleButton:hover {
  background: #ff3241;
  border-color: #ff3241;
  color: #ffffff;
}

.rightContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 629px;
  min-width: 285px;
}

.reachUs {
  color: #8a8a8a;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  line-height: 135%;
  letter-spacing: 0.25px;
  font-weight: 400;
  width: 100%;
}

.contactInfo {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-start;
}

.emailLink {
  background: linear-gradient(180deg, rgba(187, 222, 208, 1) 0%, rgba(135, 194, 170, 1) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  line-height: 135%;
  letter-spacing: 0.25px;
  font-weight: 400;
  text-decoration: none;
  transition: opacity 0.3s ease;
}

.emailLink:hover {
  opacity: 0.8;
}

.phoneNumber {
  color: #e2e2e2;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  line-height: 135%;
  letter-spacing: 0.25px;
  font-weight: 400;
}

/* Media Queries */
@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 0 20px;
  }
}

@media (max-width: 991px) {
  .contentWrapper {
    flex-direction: column;
    gap: 40px;
    top: 40px;
  }
  
  .leftContent, .rightContent {
    width: 100%;
  }
  
  .container {
    height: 460px;
  }
}

@media (max-width: 768px) {
  .contentWrapper {
    padding: 20px;
  }
  
  .messageContainer {
    font-size: 14px;
  }
  
  .scheduleButton {
    width: 100%;
  }
  
  .container {
    height: 500px;
  }
}

@media (max-width: 480px) {
  .contentWrapper {
    padding: 15px;
    top: 20px;
  }
  
  .leftContent {
    gap: 20px;
  }
  
  .container {
    height: 520px;
  }
}
