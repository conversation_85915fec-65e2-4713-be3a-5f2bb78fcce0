.container {
  position: relative;
  overflow: hidden;
  margin: 1rem auto;
  width: auto;
}

.image {
  width: 100%;
  height: 100%;
}

/* Alignment options */
.align-left {
  margin-left: 0;
  margin-right: auto;
}

.align-center {
  margin-left: auto;
  margin-right: auto;
}

.align-right {
  margin-left: auto;
  margin-right: 0;
}

/* Full width option */
.fullWidth {
  width: 100%;
  max-width: 100%;
}

/* Shadow option */
.shadow {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    margin: 0.75rem auto;
  }
  
  .shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}
