.backToTopButton {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #121212;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 0;
  overflow: visible;
}

.backToTopButton:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.buttonInner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  background-color: #121212;
}

.arrowIcon {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.backToTopButton:hover .arrowIcon {
  transform: translateY(-3px);
}

.ringAnimation {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid #ff3241; /* Ruby-red color */
  opacity: 0;
  animation: pulseRing 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}
