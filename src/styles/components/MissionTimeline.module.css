/* MissionTimeline.module.css */
.timelineContainer {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.timelineHeader {
  text-align: center;
  margin-bottom: 60px;
}

.timelineTitle {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 0 16px 0;
  background: linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.timelineSubtitle {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 400;
  line-height: 140%;
  margin: 0;
}

.timeline {
  position: relative;
  width: 100%;
}

.progressLine {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  z-index: 1;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #87C2AA 0%, #BBDED0 100%);
  border-radius: 2px;
  width: 100%;
  animation: progressAnimation 3s ease-in-out;
}

@keyframes progressAnimation {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

.timelineSteps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 2;
}

.timelineStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 140px;
  position: relative;
}

.stepNode {
  position: relative;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #87C2AA 0%, #BBDED0 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  box-shadow: 0 8px 25px rgba(135, 194, 170, 0.3);
  transition: all 0.3s ease;
  animation: nodeAnimation 0.6s ease-out;
  animation-delay: calc(var(--step-index, 0) * 0.1s);
  animation-fill-mode: both;
}

@keyframes nodeAnimation {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.timelineStep:hover .stepNode {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 35px rgba(135, 194, 170, 0.4);
}

.stepNumber {
  color: #1a1a1a;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 2px;
}

.stepIcon {
  font-size: 20px;
  line-height: 1;
}

.stepContent {
  text-align: center;
  max-width: 120px;
}

.stepTitle {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 0 8px 0;
}

.stepDescription {
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  margin: 0;
}

.stepConnector {
  position: absolute;
  top: 40px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(135, 194, 170, 0.5) 50%, transparent 100%);
  z-index: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .timelineContainer {
    padding: 50px 30px;
  }

  .timelineTitle {
    font-size: 28px;
  }

  .stepNode {
    width: 70px;
    height: 70px;
  }

  .stepIcon {
    font-size: 18px;
  }

  .stepTitle {
    font-size: 13px;
  }

  .stepDescription {
    font-size: 11px;
  }
}

@media (max-width: 991px) {
  .timelineContainer {
    padding: 40px 20px;
  }

  .timelineSteps {
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
  }

  .timelineStep {
    max-width: 120px;
    margin-bottom: 20px;
  }

  .progressLine {
    display: none;
  }

  .stepConnector {
    display: none;
  }

  .timelineSteps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .timelineTitle {
    font-size: 24px;
  }

  .timelineSubtitle {
    font-size: 16px;
  }

  .timelineSteps {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .stepNode {
    width: 60px;
    height: 60px;
  }

  .stepIcon {
    font-size: 16px;
  }

  .stepNumber {
    font-size: 12px;
  }

  .stepTitle {
    font-size: 12px;
  }

  .stepDescription {
    font-size: 10px;
  }
}

@media (max-width: 576px) {
  .timelineContainer {
    padding: 30px 15px;
  }

  .timelineSteps {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .timelineStep {
    max-width: 200px;
    margin: 0 auto;
  }
}
