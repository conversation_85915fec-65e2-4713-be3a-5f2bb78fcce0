.notificationContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  width: 100%;
}

.notification {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease-out forwards;
  background-color: #ffffff;
  color: #333333;
  border-left: 4px solid #333333;
}

.notification.success {
  border-left-color: #4caf50;
}

.notification.error {
  border-left-color: #f44336;
}

.notification.warning {
  border-left-color: #ff9800;
}

.notification.info {
  border-left-color: #2196f3;
}

.icon {
  margin-right: 12px;
  font-size: 20px;
}

.success .icon {
  color: #4caf50;
}

.error .icon {
  color: #f44336;
}

.warning .icon {
  color: #ff9800;
}

.info .icon {
  color: #2196f3;
}

.content {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  font-size: 16px;
  padding: 0;
  margin-left: 10px;
  transition: color 0.2s;
}

.closeButton:hover {
  color: #333;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Dark theme support */
[data-theme="dark"] .notification {
  background-color: #2d3748;
  color: #f7fafc;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .closeButton {
  color: #cbd5e0;
}

[data-theme="dark"] .closeButton:hover {
  color: #f7fafc;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .notificationContainer {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification {
    padding: 12px;
  }
}
