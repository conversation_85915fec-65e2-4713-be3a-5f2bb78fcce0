/* Navigation.module.css */
.nav,
.nav * {
  box-sizing: border-box;
}

.nav {
  background: rgba(18, 18, 18, 0.95);
  padding: 16px 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  backdrop-filter: blur(12.5px);
  width: 100%;
  z-index: 1000;
  height: auto;
  transform: translateY(0);
  transition: transform 0.3s ease, background 0.3s ease, box-shadow 0.3s ease;
}

.frame1000006514 {
  display: flex;
  flex-direction: row;
  gap: 40px;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  position: relative;
}

.logoContainer {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  margin-right: 10px;
  width: 40px;
  height: 40px;
}

.logoImage {
  flex-shrink: 0;
  width: 30px;
  height: 30px;
  position: relative;
  object-fit: contain;
  max-width: 100%;
}

.navLinksContainer {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}

.navLinksGroup {
  background: rgba(41, 41, 41, 0.5);
  border-radius: 8px;
  border-style: solid;
  border-color: rgba(56, 56, 56, 0.7);
  border-width: 1px;
  padding: 8px 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  align-self: stretch;
  flex: 0 0 auto;
  position: relative;
  backdrop-filter: blur(12.5px);
  width: 412px;
}

.navLinkHome {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  text-decoration: none;
}

.navLinkHomeText {
  color: #ffffff;
  text-align: center;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.1px;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navLinkServices {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  text-decoration: none;
}

.navLinkServicesText {
  color: #ababab;
  text-align: center;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.1px;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menuContainer {
  background: rgba(41, 41, 41, 0.4);
  border-radius: 8px;
  border-style: solid;
  border-color: rgba(56, 56, 56, 0.7);
  border-width: 1px;
  padding: 8px 16px;
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  backdrop-filter: blur(12.5px);
  cursor: pointer;
  margin: 0;
  width: 650px;
  text-align: center;
}

.menuButton {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  background: transparent;
  border: none;
  cursor: pointer;
}

.menuIcon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  position: relative;
  object-fit: cover;
}

.menuText {
  color: #ababab;
  text-align: center;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.1px;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contactButton {
  background: #ffffff;
  border-radius: 8px;
  border-style: solid;
  border-color: rgba(56, 56, 56, 0.7);
  border-width: 1px;
  padding: 8px 32px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 212px;
  height: 40px;
  position: relative;
  backdrop-filter: blur(2px);
  text-decoration: none;
  margin-left: 15px;
}

.contactText {
  color: #323232;
  text-align: center;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dropdown Menu */
.dropdownMenu {
  position: absolute;
  top: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background: rgba(18, 18, 18, 0.98);
  border-radius: 8px;
  padding: 2rem;
  min-width: 600px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  animation: fadeIn 0.2s ease-in-out;
  backdrop-filter: blur(12.5px);
  border: 1px solid rgba(56, 56, 56, 0.7);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(-50%) translateY(-10px); }
  to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

.dropdownMenu::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid rgba(41, 41, 41, 0.9);
}

.dropdownColumns {
  display: flex;
  gap: 2.5rem;
  justify-content: space-between;
}

.dropdownColumn {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  align-items: flex-start;
}

.dropdownColumn a {
  color: #fffef6;
  text-decoration: none;
  padding: 0.5rem 0;
  font-size: 14px;
  transition: color 0.3s ease;
  position: relative;
  font-family: "Poppins", sans-serif;
  letter-spacing: 0.1px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: left;
}

.dropdownColumn a img {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  filter: brightness(0) saturate(100%) invert(100%) sepia(8%) saturate(1090%) hue-rotate(298deg) brightness(113%) contrast(96%);
  transition: filter 0.3s ease;
}

.dropdownColumn a:hover {
  color: #ff3241;
}

.dropdownColumn a:hover img {
  filter: brightness(0) saturate(100%) invert(29%) sepia(94%) saturate(2526%) hue-rotate(343deg) brightness(99%) contrast(100%);
}

.mobileToggler {
  display: none;
  background: transparent;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: 10px;
}

.mobileMenu {
  background: rgba(18, 18, 18, 0.98);
  padding: 1rem;
  backdrop-filter: blur(12.5px);
}

.mobileMenuList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobileMenuList li {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(56, 56, 56, 0.7);
}

.mobileMenuList li:last-child {
  border-bottom: none;
}

.mobileMenuList li a {
  color: #fffef6;
  text-decoration: none;
  font-size: 14px;
  display: block;
  transition: all 0.3s ease;
  font-family: "Poppins", sans-serif;
  letter-spacing: 0.1px;
  font-weight: 500;
}

.mobileMenuList li a:hover {
  color: #ff3241;
}

.topNavCollapse {
  background: rgba(18, 18, 18, 1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.navHidden {
  transform: translateY(-100%) !important;
}

/* Media Queries */
@media (max-width: 1440px) {
  .navLinksGroup {
    width: 28vw;
  }

  .menuContainer {
    width: 45vw;
  }
}

@media (max-width: 991px) {
  .nav {
    padding: 16px;
  }

  .frame1000006514 {
    gap: 16px;
  }

  .navLinksGroup {
    display: none;
  }

  .menuContainer {
    margin-left: auto;
    width: calc(100% - 32px - 120px);
    margin: 0 16px;
  }

  .logoImage {
    width: 24px;
    height: 24px;
  }

  .dropdownMenu {
    position: fixed;
    top: 72px;
    left: 16px;
    right: 16px;
    width: calc(100% - 32px);
    min-width: auto;
    transform: none;
    border-radius: 8px;
    animation: mobileFadeIn 0.2s ease-in-out;
    padding: 16px;
  }

  @keyframes mobileFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .dropdownMenu::before {
    display: none;
  }

  .dropdownColumns {
    flex-direction: column;
    gap: 1rem;
  }

  .contactButton {
    width: auto;
    padding: 8px 16px;
  }
}

@media (max-width: 767px) {
  .nav {
    padding: 16px;
  }

  .frame1000006514 {
    gap: 16px;
  }

  .logoContainer {
    margin-right: 0;
  }

  .logoImage {
    width: 20px;
    height: 20px;
  }

  .menuContainer {
    width: calc(100% - 32px - 80px);
    padding: 8px 16px;
  }

  .dropdownMenu {
    padding: 16px;
    left: 16px;
    right: 16px;
    width: calc(100% - 32px);
  }

  .menuText {
    display: none;
  }

  .contactButton {
    width: auto;
    padding: 8px 16px;
    margin-left: 0;
  }

  .contactText {
    font-size: 14px;
  }
}
