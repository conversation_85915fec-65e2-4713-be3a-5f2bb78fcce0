/* PartnershipsSection.module.css */
.partnershipsSection {
  position: relative;
  height: 851px;
  width: 100%;
  background-color: #000000;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  margin-top: 90px;
  margin-bottom: 90px;
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
}

/* Header */
.trustWallLabel {
  color: #8a8a8a;
  font-size: 16px;
  line-height: 135%;
  letter-spacing: 0.15px;
  font-weight: 400;
  position: absolute;
  left: 80px;
  top: 40px;
  width: 900px;
}

.partnershipsTitle {
  color: #f9f4fb;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  position: absolute;
  left: 80px;
  top: 86px;
  width: 629px;
}

/* View All Button */
.viewAllButton {
  border-radius: 8px;
  border: 1px solid #f9f4fb;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 229px;
  min-width: 120px;
  position: absolute;
  right: 80px;
  top: 96px;
  color: #f9f4fb;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  font-weight: 500;
  text-decoration: none;
  white-space: nowrap;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.viewAllButton:hover {
  background-color: rgba(249, 244, 251, 0.1);
  transform: translateY(-2px);
}

/* Cards Grid */
.cardsGrid {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: flex-start;
  justify-content: center;
  width: 1280px;
  height: 574px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 180px;
}

/* Left Column */
.leftColumn {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  height: 572px;
  min-width: 285px;
  position: relative;
}

/* Top Row with Two Cards */
.topRow {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  align-content: flex-start;
  align-self: stretch;
  flex: 1;
  position: relative;
}

/* Card Styles */
.card {
  border-radius: 20px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  flex: 1;
  height: 276px;
  min-width: 285px;
  min-height: 211.5px;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
  transition: background 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.card:hover::before {
  background: rgba(0, 0, 0, 0.3);
}

.card1 {
  background: url('/src/assets/images/partnerships/mission_1.jpg') center;
  background-size: cover;
  background-repeat: no-repeat;
}

.card2 {
  background: url('/src/assets/images/partnerships/mission_2.jpg') center;
  background-size: cover;
  background-repeat: no-repeat;
}

.cardIcon {
  width: 57px;
  height: 57px;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
  mix-blend-mode: hard-light;
}

.card:hover .cardIcon {
  transform: scale(1.1);
}

.cardDetails {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  min-width: 237px;
  position: relative;
  z-index: 2;
}

.cardHeading {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  position: relative;
}

.cardCategory {
  color: #ffffff;
  font-size: 12px;
  line-height: 140%;
  letter-spacing: 0.4px;
  font-weight: 400;
  position: relative;
  align-self: stretch;
  transition: color 0.3s ease;
}

.cardTitle {
  color: #ffffff;
  font-size: 19px;
  line-height: 130%;
  letter-spacing: 0.15px;
  font-weight: 400;
  position: relative;
  align-self: stretch;
  transition: transform 0.3s ease;
}

.card:hover .cardTitle {
  transform: translateY(-2px);
}

/* Bottom Card */
.bottomCard {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  min-width: 285px;
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  background: url('/src/assets/images/partnerships/mission_3.jpg') center;
  background-size: cover;
  background-repeat: no-repeat;
}

.bottomCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* Right Column */
.rightColumn {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  height: 572px;
  min-width: 285px;
  min-height: 276px;
  position: relative;
}

.largeCard {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex: 1;
  min-width: 285px;
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  position: relative;
  overflow: hidden;
}


.largeCardImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
}

.largeCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.cardImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  transition: transform 0.5s ease;
}

.bottomCard:hover .cardImage,
.largeCard:hover .cardImage {
  transform: scale(1.05);
}

.cardOverlay {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  left: 36px;
  bottom: 44px;
  z-index: 2;
}

.cardOverlay::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: -10px;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  z-index: -1;
  transition: opacity 0.3s ease;
  opacity: 0.7;
}

.bottomCard:hover .cardOverlay::before,
.largeCard:hover .cardOverlay::before {
  opacity: 0.9;
}

/* Media Queries */
@media (max-width: 1200px) {
  .partnershipsTitle {
    font-size: 48px;
    width: 500px;
  }

  .viewAllButton {
    right: 40px;
  }

  .cardsGrid {
    width: 90%;
    padding: 0 20px;
  }
}

@media (max-width: 992px) {
  .partnershipsSection {
    height: auto;
    padding-bottom: 60px;
  }

  .trustWallLabel {
    position: relative;
    left: 0;
    top: 0;
    padding: 40px 40px 0;
    width: 100%;
  }

  .partnershipsTitle {
    position: relative;
    left: 0;
    top: 0;
    padding: 0 40px;
    width: 100%;
    font-size: 36px;
    margin-bottom: 40px;
  }

  .viewAllButton {
    position: relative;
    right: auto;
    top: auto;
    margin: 0 auto 40px;
  }

  .cardsGrid {
    position: relative;
    left: 0;
    top: 0;
    transform: none;
    width: 100%;
    height: auto;
    flex-direction: column;
    padding: 0 40px;
  }

  .leftColumn, .rightColumn {
    width: 100%;
    height: auto;
  }

  .card, .bottomCard, .largeCard {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .trustWallLabel, .partnershipsTitle {
    padding-left: 20px;
    padding-right: 20px;
  }

  .cardsGrid {
    padding: 0 20px;
  }

  .topRow {
    flex-direction: column;
  }

  .card {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .partnershipsTitle {
    font-size: 28px;
  }

  .viewAllButton {
    width: calc(100% - 40px);
  }

  .cardTitle {
    font-size: 16px;
  }
}
