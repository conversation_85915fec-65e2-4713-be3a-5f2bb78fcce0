/* FounderStoryLightbox.module.css */
.lightboxOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.lightboxContainer {
  background: #1a1a1a;
  border-radius: 16px;
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.closeButton {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 24px;
  cursor: pointer;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.closeButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.lightboxContent {
  display: flex;
  flex-direction: row;
  min-height: 600px;
}

.imageSection {
  flex: 0 0 40%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
}

.founderImage {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 12px;
  object-fit: cover;
}

.contentSection {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.headerContainer {
  margin-bottom: 24px;
}

.title {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 0 12px 0;
  background: linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.founderName {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 500;
  line-height: 120%;
  margin: 0;
  opacity: 0.9;
}

.divider {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, #87C2AA 0%, transparent 100%);
  margin: 24px 0;
}

.storyContainer {
  flex: 1;
  margin-bottom: 32px;
}

.storyText {
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 160%;
  margin: 0 0 20px 0;
  opacity: 0.9;
}

.storyText:last-child {
  margin-bottom: 0;
}

.buttonsContainer {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.contactButton {
  background: linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%);
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
}

.contactButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(187, 222, 208, 0.3);
}

.backButton {
  background: transparent;
  color: #ffffff;
  border: 1px solid #ffffff;
  border-radius: 8px;
  padding: 16px 32px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 0 0 auto;
  min-width: 120px;
}

.backButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .lightboxContent {
    flex-direction: column;
  }
  
  .imageSection {
    flex: none;
    padding: 30px 30px 20px 30px;
  }
  
  .founderImage {
    max-width: 250px;
  }
  
  .contentSection {
    padding: 20px 30px 30px 30px;
  }
  
  .title {
    font-size: 28px;
  }
  
  .founderName {
    font-size: 20px;
  }
  
  .buttonsContainer {
    flex-direction: column;
  }
  
  .contactButton,
  .backButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .lightboxOverlay {
    padding: 10px;
  }
  
  .imageSection,
  .contentSection {
    padding: 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .founderName {
    font-size: 18px;
  }
  
  .storyText {
    font-size: 14px;
  }
}
