/* BenefitsSection.module.css */
.benefitsSection {
  position: relative;
  height: 719px;
  width: 100%;
  background-color: #000000;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  margin-top: 90px;
  margin-bottom: 90px;
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
}

/* Header */
.header {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: absolute;
  left: 80px;
  top: 40px;
}

.benefitsLabel {
  color: #8a8a8a;
  font-size: 16px;
  line-height: 135%;
  letter-spacing: 0.15px;
  font-weight: 400;
}

.benefitsTitle {
  color: #e2e2e2;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  width: 870px;
}

.benefitsDescription {
  color: #d0d0d0;
  font-size: 16px;
  line-height: 130%;
  letter-spacing: 0.5px;
  font-weight: 400;
  width: 631px;
}

/* Success Stories Button */
.successButton {
  border-radius: 8px;
  border: 1px solid #f7f7f7;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 229px;
  min-width: 120px;
  position: absolute;
  right: 80px;
  top: 94px;
  color: #f7f7f7;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  font-weight: 500;
  text-decoration: none;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.successButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Bento Grid */
.bentoGrid {
  display: flex;
  flex-direction: row;
  gap: 20px;
  position: absolute;
  left: 80px;
  top: 222px;
  width: 1280px;
}

/* Card 1 */
.card1 {
  background: linear-gradient(
    90deg,
    rgba(50, 50, 50, 1) 0%,
    rgba(50, 50, 50, 1) 100%,
    rgba(80, 80, 80, 1) 100.01%
  );
  border-radius: 20px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 380px;
  min-width: 285px;
  position: relative;
  overflow: hidden;
}

/* Column 2 */
.column2 {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  height: 380px;
}

/* Card 2 */
.card2 {
  background: linear-gradient(
    66.62deg,
    rgba(50, 50, 50, 1) 0%,
    rgba(50, 50, 50, 1) 0%,
    rgba(32, 39, 36, 1) 72.42%,
    rgba(50, 50, 50, 1) 100%
  );
  border-radius: 20px;
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 285px;
  position: relative;
  overflow: hidden;
}

.card2Bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  left: 0;
  top: 0;
  mix-blend-mode: soft-light;
}

/* Card 3 */
.card3 {
  background: linear-gradient(
    180deg,
    rgba(137, 16, 28, 1) 0%,
    rgba(255, 95, 104, 1) 100%
  );
  border-radius: 20px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 285px;
  position: relative;
  overflow: hidden;
}

/* Card 4 */
.card4 {
  background: linear-gradient(
    90deg,
    rgba(50, 50, 50, 1) 0%,
    rgba(50, 50, 50, 1) 100%,
    rgba(80, 80, 80, 1) 100.01%
  );
  border-radius: 20px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 380px;
  min-width: 285px;
  position: relative;
  overflow: hidden;
}

/* Card 5 */
.card5 {
  background: linear-gradient(
    90deg,
    rgba(50, 50, 50, 1) 0%,
    rgba(50, 50, 50, 1) 100%,
    rgba(80, 80, 80, 1) 100.01%
  );
  border-radius: 20px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 380px;
  min-width: 285px;
  position: relative;
  overflow: hidden;
}

/* Card Content */
.cardContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.cardTitle {
  color: #ffffff;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

/* Background Elements */
.starsBackground {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  mix-blend-mode: overlay;
  display: flex;
  align-items: center;
  justify-content: center;
}

.starsBackgroundSvg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Solar System */
.solarSystem {
  opacity: 0.5;
  width: 304px;
  height: 300px;
  position: absolute;
  left: 730.67px;
  top: 253px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.solarSystemSvg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Wave Background */
.waveBg {
  width: 100%;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  overflow: hidden;
}

.maskGroup {
  opacity: 0.4;
  height: auto;
  position: absolute;
  left: -85px;
  top: 90px;
}



/* Interdisciplinary Research Text */
.interdisciplinaryResearch {
  color: #ffffff;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  position: absolute;
  left: 422.51px;
  top: 260px;
  width: 214.4px;
}

/* Media Queries */
@media (max-width: 1200px) {
  .bentoGrid {
    width: 100%;
    padding: 0 80px;
    left: 0;
  }

  .benefitsTitle {
    font-size: 48px;
    width: 700px;
  }

  .benefitsDescription {
    width: 500px;
  }
}

@media (max-width: 992px) {
  .benefitsSection {
    height: auto;
    padding-bottom: 60px;
  }

  .header {
    position: relative;
    left: 0;
    top: 0;
    padding: 40px 40px 0;
  }

  .benefitsTitle {
    font-size: 36px;
    width: 100%;
  }

  .benefitsDescription {
    width: 100%;
  }

  .successButton {
    position: relative;
    right: auto;
    top: auto;
    margin: 30px 0 0 40px;
  }

  .bentoGrid {
    position: relative;
    left: 0;
    top: 0;
    flex-direction: column;
    padding: 40px;
    margin-top: 30px;
  }

  .card1, .card4, .card5 {
    height: 300px;
  }

  .column2 {
    height: auto;
  }

  .waveBg, .interdisciplinaryResearch {
    display: none;
  }

  /* Keep stars background visible on all screens */
  .starsBackground {
    position: relative;
    width: 100%;
    height: 200px;
    margin: 20px 0;
    opacity: 0.7;
  }

  /* Keep solar system visible on all screens */
  .solarSystem {
    position: relative;
    left: auto;
    top: auto;
    width: 200px;
    height: 200px;
    margin: 20px auto;
    opacity: 0.7;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 40px 20px 0;
  }

  .benefitsTitle {
    font-size: 32px;
  }

  .successButton {
    margin: 30px 0 0 20px;
  }

  .bentoGrid {
    padding: 20px;
  }

  /* Stars background responsive for tablet */
  .starsBackground {
    height: 150px;
    margin: 15px 0;
  }

  /* Solar system responsive for tablet */
  .solarSystem {
    width: 150px;
    height: 150px;
    margin: 15px auto;
  }
}

@media (max-width: 576px) {
  .benefitsTitle {
    font-size: 28px;
  }

  .benefitsDescription {
    font-size: 14px;
  }

  .successButton {
    width: calc(100% - 40px);
  }

  .cardTitle {
    font-size: 18px;
  }

  /* Mobile card specifications */
  .bentoGrid {
    gap: 25px;
    padding: 32px;
  }

  .card1, .card2, .card3, .card4, .card5 {
    min-height: 210px;
    width: auto;
    padding: 32px;
    margin: 0;
  }

  .column2 {
    gap: 25px;
  }

  /* Stars background responsive for mobile */
  .starsBackground {
    height: 120px;
    margin: 10px 0;
    opacity: 0.6;
  }

  /* Solar system responsive for mobile */
  .solarSystem {
    width: 120px;
    height: 120px;
    margin: 10px auto;
    opacity: 0.6;
  }
}
