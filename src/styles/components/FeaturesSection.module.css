/* FeaturesSection.module.css */
.featuresSection {
  position: relative;
  height: 754px;
  width: 100%;
  background-color: #000000;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  margin-top: 90px;
  margin-bottom: 90px;
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
}

/* Header */
.capabilitiesLabel {
  color: #8a8a8a;
  font-size: 16px;
  line-height: 135%;
  letter-spacing: 0.15px;
  font-weight: 400;
  position: absolute;
  left: 80px;
  top: 40px;
  width: 900px;
}

.featuresTitle {
  color: #e2e2e2;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  position: absolute;
  left: 80px;
  top: 81px;
  width: 870px;
}

/* Feature Slider */
.featureSlider {
  width: 545px;
  height: 250px;
  position: absolute;
  left: 80px;
  top: 282px;
  overflow: hidden;
}

.featureCard {
  width: 545px;
  height: 200px;
  position: relative;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.slideOut {
  transform: translateX(-20px);
  opacity: 0;
}

.featureContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
}

.objectsIcon {
  width: 48px;
  height: 24px;
}

.featureTextContainer {
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.featureTitle {
  color: #ffffff;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  width: 472px;
  height: 34px;
}

.featureDescription {
  color: #ababab;
  font-size: 16px;
  line-height: 130%;
  letter-spacing: 0.5px;
  font-weight: 400;
}

/* Slider Indicator */
.sliderIndicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
}

.sliderTrack {
  width: 100%;
  height: 1px;
  background-color: #323232;
  position: relative;
}

.sliderProgress {
  height: 1px;
  background-color: #EF3B47;
  position: absolute;
  left: 0;
  top: 0;
  transition: width 0.5s ease;
}

/* See All Button */
.seeAllButton {
  border-radius: 8px;
  border: 1px solid #ffffff;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 229px;
  min-width: 120px;
  position: absolute;
  left: 80px;
  top: 593px;
  color: #ffffff;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.25px;
  font-weight: 500;
  text-decoration: none;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.seeAllButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Feature Image */
.imageContainer {
  position: relative;
}

.featureImage {
  width: 620px;
  height: 436px;
  position: absolute;
  right: 80px;
  top: 159px;
  mix-blend-mode: hard-light;
  object-fit: cover;
  border-radius: 1.2em;
}

/* Hardware Label */
.hardwareLabel {
  background: rgba(54, 54, 54, 0.5);
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
  justify-content: flex-start;
  width: 351px;
  position: absolute;
  right: 215px;
  top: 603px;
  backdrop-filter: blur(10px);
  color: #eeeeee;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.1px;
  font-weight: 500;
  overflow: visible;
}

.boltIcon {
  width: 24px;
  height: 24px;
}

.labelTextContainer {
  position: relative;
  height: 24px;
  overflow: hidden;
  width: 100%;
  display: flex;
  align-items: center;
  flex: 1;
}

.labelText {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 0.6s ease, transform 0.6s ease;
  white-space: nowrap;
  width: 100%;
}

.activeLabel {
  opacity: 1;
  transform: translateY(-50%);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.inactiveLabel {
  opacity: 0;
  transform: translateY(10px) translateX(0);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

/* Media Queries */
@media (max-width: 1200px) {
  .featuresTitle {
    font-size: 48px;
    width: 700px;
  }

  .featureImage {
    width: 500px;
    height: 350px;
    right: 40px;
  }

  .hardwareLabel {
    right: 115px;
  }
}

@media (max-width: 992px) {
  .featuresSection {
    height: auto;
    padding-bottom: 60px;
  }

  .capabilitiesLabel {
    position: relative;
    left: 0;
    top: 0;
    padding: 40px 40px 0;
    width: 100%;
  }

  .featuresTitle {
    position: relative;
    left: 0;
    top: 0;
    padding: 0 40px;
    width: 100%;
    font-size: 36px;
    margin-bottom: 40px;
  }

  .featureSlider {
    position: relative;
    left: 0;
    top: 0;
    width: calc(100% - 80px);
    height: 250px;
    margin: 0 40px 40px;
  }

  .featureCard {
    width: 100%;
    height: auto;
  }

  .featureTitle, .featureDescription {
    width: 100%;
    height: auto;
  }

  .sliderIndicator {
    bottom: 0;
  }

  .seeAllButton {
    position: relative;
    left: 40px;
    top: 0;
    margin-bottom: 40px;
  }

  .imageContainer {
    display: flex;
    justify-content: center;
    padding: 0 40px;
  }

  .featureImage {
    position: relative;
    right: auto;
    top: auto;
    width: 100%;
    max-width: 500px;
    height: auto;
  }

  .hardwareLabel {
    position: relative;
    right: auto;
    top: auto;
    width: 80%;
    max-width: 351px;
    margin: -60px auto 0;
  }
}

@media (max-width: 768px) {
  .capabilitiesLabel, .featuresTitle {
    padding-left: 20px;
    padding-right: 20px;
  }


  .seeAllButton {
    left: 20px;
  }

  .imageContainer {
    padding: 0 20px;
  }

  .featuresTitle {
    font-size: 32px;
  }

  .featureTitle {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  /* Add 20px margin to the section */

  .featuresTitle {
    font-size: 28px;
  }

  .featureTitle {
    font-size: 18px;
  }

  .featureDescription {
    font-size: 14px;
  }

  .seeAllButton {
    width: calc(100% - 40px);
  }

  /* Move hardware label below the feature image */
  .hardwareLabel {
    position: relative;
    right: auto;
    top: auto;
    font-size: 12px;
    display: flex;
    justify-content: center;
  }

  /* Ensure image container has proper spacing */
  .imageContainer {
    margin-bottom: 0;
  }
}
