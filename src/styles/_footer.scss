// Footer styles
@use "sass:color";

.footer {
  padding-top: 4.5rem;
  padding-bottom: 4rem;
  background-color: $primary-color;

  .footer-col {
    margin-bottom: 2rem;

    &.first {
      margin-right: 1.5rem;
    }

    img {
      max-width: 100%;
      height: auto;
      margin-bottom: 1.5rem;
    }

    h4 {
      margin-bottom: 1.5rem;
    }

    p {
      margin-bottom: 1.5rem;
    }

    .list-unstyled {
      .media {
        margin-bottom: 0.5rem;

        i {
          margin-right: 0.5rem;
          color: $turquoise;
        }
      }
    }

    &.last {
      margin-right: 0;

      .fa-stack {
        width: 2em;
        margin-bottom: 0.75rem;
        margin-right: 0.375rem;
        font-size: 1.5rem;

        &:last-child {
          margin-right: 0;
        }

        a {
          color: $white;
          @include transition;

          &:hover {
            color: $turquoise;
          }

          i.fa-circle {
            color: $dark-gray;
            @include transition;
          }

          i.fa-stack-1x {
            color: $white;
            @include transition;
          }

          &:hover i.fa-circle {
            color: $turquoise;
          }
        }
      }
    }
  }
}

.copyright {
  padding-top: 1.75rem;
  padding-bottom: 0.75rem;
  background-color: color.adjust($primary-color, $lightness: -5%);
  text-align: center;

  .p-small {
    margin-bottom: 0;
    color: $white;

    a {
      color: $white;
      text-decoration: none;

      &:hover {
        color: $turquoise;
      }
    }
  }
}

@include respond-to(md) {
  .footer {
    .footer-col {
      &.middle {
        margin-bottom: 2.5rem;
      }
    }
  }
}
