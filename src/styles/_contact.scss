// Contact styles

.form-2 {
  padding-top: 7rem;
  padding-bottom: 6.25rem;
  background: url('/src/assets/images/contact-background.jpg') center center no-repeat;
  background-size: cover;
  
  h2 {
    margin-bottom: 1.5rem;
    text-align: center;
  }
  
  .list-unstyled {
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.6rem;
    text-align: center;
    
    .address {
      margin-bottom: 0.75rem;
    }
    
    i {
      margin-right: 0.5rem;
      font-size: 0.875rem;
      color: $turquoise;
    }
    
    a {
      color: $turquoise;
      text-decoration: none;
    }
  }
  
  #my-cal-inline {
    width: 100%;
    height: 600px;
    overflow: scroll;
    z-index: 999;
  }
}

@include respond-to(md) {
  .form-2 {
    .list-unstyled {
      max-width: 18.75rem;
      margin-right: auto;
      margin-left: auto;
      text-align: left;
    }
  }
}
