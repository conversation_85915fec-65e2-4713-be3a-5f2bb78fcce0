/* TestPage.module.css */

.testPage {
  background-color: #001718;
  min-height: 100vh;
  position: relative;
}

/* Hero Section with Video Background */
.heroSection {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoContainer {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.videoBackground {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.6;
  transition: opacity 0.5s ease, transform 0.5s ease;
  transform: scale(1.1);
}

.videoBackground.videoActive {
  opacity: 1;
  transform: scale(1);
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 23, 24, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  cursor: pointer;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.videoOverlay.overlayHidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.playButton {
  transition: transform 0.3s ease;
}

.playButton:hover {
  transform: scale(1.1);
}

.heroContent {
  position: relative;
  z-index: 3;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  pointer-events: none;
}

.contentContainer {
  max-width: 1320px;
  margin: 0 auto;
  padding: 0 60px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.heroTitle {
  color: #fffef6;
  font-family: 'Poppins', sans-serif;
  font-size: 72px;
  font-weight: 300;
  line-height: 110%;
  margin: 0 0 24px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.heroSubtitle {
  color: #bcd8d5;
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 300;
  line-height: 140%;
  margin: 0 0 60px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.statsContainer {
  display: flex;
  gap: 80px;
  margin-top: auto;
  margin-bottom: 80px;
}

.statItem {
  text-align: left;
}

.statNumber {
  color: #fffef6;
  font-family: 'Poppins', sans-serif;
  font-size: 64px;
  font-weight: 200;
  line-height: 100%;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.statLabel {
  color: #bcd8d5;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Canvas Section */
.canvasSection {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #001718 0%, #17242d 50%, #001718 100%);
  display: flex;
  align-items: center;
  padding: 120px 0;
}

.canvasContainer {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1320px;
  margin: 0 auto;
  padding: 0 60px;
}

.canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: transparent;
  cursor: crosshair;
  transition: opacity 0.3s ease;
}

.canvasContent {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.canvasTitle {
  color: #fffef6;
  font-family: 'Poppins', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 0 32px 0;
  background: linear-gradient(135deg, #bcd8d5 0%, #107e7d 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.canvasDescription {
  color: #bcd8d5;
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 300;
  line-height: 160%;
  margin: 0 0 60px 0;
  opacity: 0.9;
}

.canvasFeatures {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.feature {
  background: rgba(23, 36, 45, 0.3);
  border: 1px solid rgba(188, 216, 213, 0.1);
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  backdrop-filter: blur(10px);
}

.feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(16, 126, 125, 0.2);
}

.featureIcon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.feature h3 {
  color: #bcd8d5;
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 0 16px 0;
}

.feature p {
  color: #fffef6;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 300;
  line-height: 150%;
  margin: 0;
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .contentContainer,
  .canvasContainer {
    padding-left: 40px;
    padding-right: 40px;
  }

  .heroTitle {
    font-size: 60px;
  }

  .statsContainer {
    gap: 60px;
  }
}

@media (max-width: 991px) {
  .heroTitle {
    font-size: 48px;
  }

  .heroSubtitle {
    font-size: 20px;
  }

  .statsContainer {
    flex-direction: column;
    gap: 40px;
    align-items: flex-start;
  }

  .statNumber {
    font-size: 48px;
  }

  .canvasTitle {
    font-size: 36px;
  }

  .canvasFeatures {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .contentContainer,
  .canvasContainer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .heroTitle {
    font-size: 36px;
  }

  .heroSubtitle {
    font-size: 18px;
    margin-bottom: 40px;
  }

  .statsContainer {
    margin-bottom: 60px;
  }

  .statNumber {
    font-size: 40px;
  }

  .statLabel {
    font-size: 12px;
  }

  .canvasSection {
    padding: 80px 0;
  }

  .canvasTitle {
    font-size: 28px;
  }

  .canvasDescription {
    font-size: 16px;
  }

  .feature {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 28px;
  }

  .heroSubtitle {
    font-size: 16px;
  }

  .statNumber {
    font-size: 32px;
  }

  .playButton svg {
    width: 60px;
    height: 60px;
  }
}
