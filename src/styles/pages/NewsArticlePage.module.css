/* NewsArticlePage.module.css */

/* General Styles */
.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

/* Article Hero Header */
.articleHero {
  position: relative;
  height: 600px;
  color: #ffffff;
}

/* Article Summary Section */
.summarySection {
  background: linear-gradient(
    180deg,
    #89101C 0%,
    #FF5F68 100%
  );
  background-image: url('../../assets/images/midsec2.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 40px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  max-height: 288px;
  height: 288px;
  box-sizing: border-box;
}

.summaryContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 1280px;
  position: relative;
}

.summaryContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  flex: 1;
  position: relative;
}

.summaryWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 1280px;
  min-width: 343px;
  position: relative;
}

.summaryInner {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  min-width: 285px;
  position: relative;
  margin-left: 60px;
}

.summaryText {
  color: #f9f4fb;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  position: relative;
  width: 626px;
  margin: 0;
  max-height: 208px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.heroImageContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
  z-index: 2;
}

.heroContent {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 80px 0 60px;
  z-index: 3;
}

.backLink {
  display: inline-block;
  color: #e0e0e0;
  text-decoration: none;
  margin-bottom: 30px;
  font-size: 16px;
  transition: color 0.3s ease;
  position: relative;
  padding-left: 20px;
}

.backLink:before {
  content: '←';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.backLink:hover {
  color: #ff3241;
}

.articleMeta {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.articleCategory {
  color: #ff3241;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
}

.articleDate {
  color: #e0e0e0;
  font-size: 14px;
}

.articleTitle {
  color: #ffffff;
  font-size: 48px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 20px;
  max-width: 900px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.articleAuthor {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.authorAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.authorName {
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 500;
}

/* Article Content */
.articleContent {
  padding: 80px 0;
  background-color: #121212;
  color: #e0e0e0;
}

.contentWrapper {
  display: flex;
  gap: 60px;
}

.mainContent {
  flex: 0 0 70%;
  font-size: 18px;
  line-height: 1.8;
}

.mainContent h2 {
  color: #f9f4fb;
  font-size: 32px;
  margin: 40px 0 20px;
  font-weight: 500;
}

.mainContent p {
  margin-bottom: 20px;
}

.mainContent ul {
  margin-bottom: 20px;
  padding-left: 20px;
}

.mainContent li {
  margin-bottom: 10px;
}

.sidebar {
  flex: 0 0 25%;
}

.sidebarTitle {
  color: #f9f4fb;
  font-size: 20px;
  margin-bottom: 20px;
  font-weight: 500;
}

.shareSection {
  margin-bottom: 40px;
}

.socialLinks {
  display: flex;
  gap: 10px;
}

.socialLink {
  display: inline-block;
  padding: 8px 16px;
  background-color: #1e1e1e;
  color: #f9f4fb;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.socialLink:hover {
  background-color: #ff3241;
  transform: translateY(-2px);
}

.tagsSection {
  margin-bottom: 40px;
}

.tagsList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  display: inline-block;
  padding: 6px 12px;
  background-color: #1e1e1e;
  color: #8a8a8a;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.tag:hover {
  background-color: #ff3241;
  color: #ffffff;
}

/* Related Articles */
.relatedArticles {
  padding: 80px 0;
  background-color: #080809;
}

.relatedTitle {
  color: #f9f4fb;
  font-size: 36px;
  margin-bottom: 40px;
  font-weight: 500;
}

.relatedGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.relatedCard {
  background-color: #1e1e1e;
  border-radius: 20px;
  overflow: hidden;
  text-decoration: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.relatedCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.relatedImageContainer {
  height: 200px;
  overflow: hidden;
}

.relatedImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.relatedCard:hover .relatedImage {
  transform: scale(1.05);
}

.relatedContent {
  padding: 20px;
}

.relatedCategory {
  color: #ff3241;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  display: block;
  margin-bottom: 10px;
}

.relatedCardTitle {
  color: #f9f4fb;
  font-size: 18px;
  line-height: 130%;
  font-weight: 500;
  transition: color 0.3s ease;
}

.relatedCard:hover .relatedCardTitle {
  color: #ff3241;
}

/* Call to Action */
.ctaSection {
  padding: 80px 0;
  background-color: #121212;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.ctaTitle {
  color: #f9f4fb;
  font-size: 36px;
  margin-bottom: 20px;
  font-weight: 500;
}

.ctaDescription {
  color: #e0e0e0;
  font-size: 18px;
  line-height: 160%;
  margin-bottom: 30px;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.ctaButton {
  display: inline-block;
  background-color: #ff3241;
  color: #ffffff;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.ctaButton:hover {
  background-color: #e62b39;
  transform: translateY(-2px);
}

.ctaButtonOutline {
  display: inline-block;
  background-color: transparent;
  border: 1px solid #f9f4fb;
  color: #f9f4fb;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.ctaButtonOutline:hover {
  background-color: rgba(249, 244, 251, 0.1);
  transform: translateY(-2px);
}

/* Media Queries */
@media (max-width: 1200px) {
  .articleTitle {
    font-size: 42px;
  }

  .mainContent {
    font-size: 16px;
  }

  .articleHero {
    height: 550px;
  }

  .summaryContainer,
  .summaryWrapper {
    width: 100%;
    padding: 0 20px;
  }

  .summaryText {
    width: 100%;
    max-width: 626px;
  }
}

@media (max-width: 992px) {
  .contentWrapper {
    flex-direction: column;
  }

  .mainContent,
  .sidebar {
    flex: 0 0 100%;
  }

  .sidebar {
    display: flex;
    gap: 40px;
  }

  .shareSection,
  .tagsSection {
    flex: 1;
    margin-bottom: 0;
  }

  .relatedGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .articleHero {
    height: 500px;
  }

  .heroContent {
    padding: 60px 0 40px;
  }

  .articleTitle {
    font-size: 32px;
  }

  .summarySection {
    padding: 40px 10px;
    height: 288px;
    max-height: 288px;
  }

  .summaryText {
    font-size: 20px;
  }

  .summaryInner {
    margin-left: 30px;
  }

  .articleContent {
    padding: 60px 0;
  }

  .mainContent h2 {
    font-size: 28px;
  }

  .sidebar {
    flex-direction: column;
    gap: 30px;
  }

  .shareSection,
  .tagsSection {
    margin-bottom: 0;
  }

  .relatedArticles {
    padding: 60px 0;
  }

  .relatedTitle {
    font-size: 28px;
  }

  .relatedGrid {
    grid-template-columns: 1fr;
  }

  .ctaSection {
    padding: 60px 0;
  }

  .ctaTitle {
    font-size: 28px;
  }

  .ctaButtons {
    flex-direction: column;
    gap: 15px;
  }

  .ctaButton,
  .ctaButtonOutline {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .articleHero {
    height: 450px;
  }

  .heroContent {
    padding: 40px 0 30px;
  }

  .articleTitle {
    font-size: 28px;
  }

  .summaryInner {
    margin-left: 20px;
  }

  .summarySection {
    padding: 40px 10px;
  }

  .summaryText {
    font-size: 18px;
  }
}
