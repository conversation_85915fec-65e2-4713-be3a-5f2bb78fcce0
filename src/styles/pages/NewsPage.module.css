/* NewsPage.module.css */

/* General Styles */
.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

/* Hero Section */
.heroSection {
  position: relative;
  height: 500px;
  background-image: url('../../assets/images/news/spacenews.png');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #ffffff;
  text-align: left;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
}

.heroContent {
  position: relative;
  z-index: 2;
  max-width: 800px;
  padding: 0 0 0 60px;
  text-align: left;
}

.label {
  color: #8a8a8a;
  font-size: 16px;
  line-height: 135%;
  letter-spacing: 0.15px;
  font-weight: 400;
  margin-bottom: 16px;
}

.title {
  color: #f9f4fb;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: -0.5px;
  font-weight: 500;
  margin-bottom: 24px;
}

.subtitle {
  color: #e0e0e0;
  font-size: 20px;
  line-height: 150%;
  letter-spacing: 0.15px;
  font-weight: 400;
  max-width: 700px;
  margin: 0;
}

/* Summary Section */
.summarySection {
  background: linear-gradient(
    180deg,
    #89101C 0%,
    #FF5F68 100%
  );
  background-image: url('/src/assets/images/midsec2.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 40px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  max-height: 288px;
  height: 288px;
  box-sizing: border-box;
}

.summaryContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 1280px;
  position: relative;
}

.summaryContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  flex: 1;
  position: relative;
}

.summaryWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 1280px;
  min-width: 343px;
  position: relative;
}

.summaryInner {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  min-width: 285px;
  position: relative;
  margin-left: 60px;
}

.summaryText {
  color: #f9f4fb;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  position: relative;
  width: 626px;
  margin: 0;
  max-height: 208px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* Featured Article Section */
.featuredSection {
  padding: 80px 0;
  background-color: #080809;
}

.featuredContent {
  display: flex;
  gap: 40px;
  align-items: center;
}

.featuredImageContainer {
  flex: 0 0 50%;
  height: 400px;
  border-radius: 20px;
  overflow: hidden;
}

.featuredImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.featuredImageContainer:hover .featuredImage {
  transform: scale(1.05);
}

.featuredDetails {
  flex: 0 0 50%;
  padding: 20px;
}

.featuredMeta {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.featuredCategory {
  color: #ff3241;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
}

.featuredDate {
  color: #8a8a8a;
  font-size: 14px;
}

.featuredTitle {
  color: #f9f4fb;
  font-size: 36px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 20px;
}

.featuredAuthor {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.authorAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #687e7c;
}

.authorName {
  color: #8a8a8a;
  font-size: 16px;
  font-weight: 500;
}

.featuredExcerpt {
  color: #e0e0e0;
  font-size: 18px;
  line-height: 160%;
  margin-bottom: 30px;
}

.readMoreButton {
  display: inline-block;
  background-color: transparent;
  border: 1px solid #f9f4fb;
  color: #f9f4fb;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.readMoreButton:hover {
  background-color: rgba(249, 244, 251, 0.1);
  transform: translateY(-2px);
}

/* News Articles Section */
.newsSection {
  padding: 80px 0;
  background-color: #121212;
}

.sectionTitleContainer {
  text-align: center;
  margin-bottom: 40px;
}

.sectionTitle {
  color: #f9f4fb;
  font-size: 36px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 16px;
}

.sectionSubtitle {
  color: #e0e0e0;
  font-size: 18px;
  line-height: 160%;
  max-width: 700px;
  margin: 0 auto;
}

.categoryFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 40px;
  justify-content: center;
}

.categoryButton {
  background-color: transparent;
  border: 1px solid #687e7c;
  color: #8a8a8a;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.categoryButton:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.categoryButton.active {
  background-color: #ff3241;
  border-color: #ff3241;
  color: #ffffff;
}

.newsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.newsCard {
  background-color: #1e1e1e;
  border-radius: 20px;
  overflow: hidden;
  text-decoration: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.newsCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.newsCardImageContainer {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.newsCardImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.newsCard:hover .newsCardImage {
  transform: scale(1.05);
}

.cardIcon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  transition: transform 0.3s ease;
  z-index: 2;
}

.newsCard:hover .cardIcon {
  transform: scale(1.1);
}

.newsCardContent {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.newsCardMeta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.newsCardCategory {
  color: #ff3241;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.newsCardDate {
  color: #8a8a8a;
  font-size: 12px;
}

.newsCardTitle {
  color: #f9f4fb;
  font-size: 20px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 16px;
  transition: color 0.3s ease;
}

.newsCard:hover .newsCardTitle {
  color: #ff3241;
}

.newsCardExcerpt {
  color: #e0e0e0;
  font-size: 14px;
  line-height: 160%;
  margin-bottom: 20px;
  flex: 1;
}

.newsCardAuthor {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: auto;
}

.loadMoreContainer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.loadMoreButton {
  background-color: transparent;
  border: 1px solid #f9f4fb;
  color: #f9f4fb;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.loadMoreButton:hover {
  background-color: rgba(249, 244, 251, 0.1);
  transform: translateY(-2px);
}

/* Newsletter Section */
.newsletterSection {
  padding: 80px 0;
  background-color: #080809;
}

.newsletterContent {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.newsletterTitle {
  color: #f9f4fb;
  font-size: 36px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 20px;
}

.newsletterDescription {
  color: #e0e0e0;
  font-size: 18px;
  line-height: 160%;
  margin-bottom: 30px;
}

.newsletterForm {
  display: flex;
  gap: 10px;
  max-width: 500px;
  margin: 0 auto;
}

.newsletterInput {
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #687e7c;
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 16px;
}

.newsletterInput:focus {
  outline: none;
  border-color: #ff3241;
}

.newsletterButton {
  background-color: #ff3241;
  border: none;
  color: #ffffff;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.newsletterButton:hover {
  background-color: #e62b39;
  transform: translateY(-2px);
}

/* Media Queries */
@media (max-width: 1200px) {
  .title {
    font-size: 48px;
  }

  .featuredTitle {
    font-size: 32px;
  }

  .sectionTitle {
    font-size: 32px;
  }

  .summaryContainer,
  .summaryWrapper {
    width: 100%;
    padding: 0 20px;
  }

  .summaryText {
    width: 100%;
    max-width: 626px;
  }
}

@media (max-width: 992px) {
  .featuredContent {
    flex-direction: column;
  }

  .featuredImageContainer,
  .featuredDetails {
    flex: 0 0 100%;
    width: 100%;
  }

  .featuredImageContainer {
    height: 300px;
  }

  .newsGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .heroSection {
    height: 400px;
  }

  .heroContent {
    padding: 0 0 0 30px;
  }

  .title {
    font-size: 36px;
  }

  .subtitle {
    font-size: 18px;
  }

  .featuredSection,
  .newsSection,
  .newsletterSection {
    padding: 60px 0;
  }

  .featuredTitle {
    font-size: 28px;
  }

  .sectionTitle {
    font-size: 28px;
  }

  .sectionSubtitle {
    font-size: 16px;
  }

  .featuredExcerpt {
    font-size: 16px;
  }

  .summarySection {
    padding: 40px 10px;
    height: 288px;
    max-height: 288px;
  }

  .summaryText {
    font-size: 20px;
  }

  .summaryInner {
    margin-left: 30px;
  }

  .newsGrid {
    grid-template-columns: 1fr;
  }

  .newsletterForm {
    flex-direction: column;
  }

  .newsletterInput,
  .newsletterButton {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .title {
    font-size: 32px;
  }

  .heroContent {
    padding: 0 0 0 20px;
  }

  .featuredTitle {
    font-size: 24px;
  }

  .sectionTitle {
    font-size: 24px;
  }

  .sectionSubtitle {
    font-size: 14px;
  }

  .summaryInner {
    margin-left: 20px;
  }

  .summarySection {
    padding: 40px 10px;
  }

  .summaryText {
    font-size: 18px;
  }

  .categoryFilter {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 10px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .categoryFilter::-webkit-scrollbar {
    display: none;
  }
}
