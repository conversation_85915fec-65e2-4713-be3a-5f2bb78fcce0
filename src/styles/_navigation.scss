// Navigation styles

.navbar {
  background-color: transparent;
  font: $font-weight-semibold 0.875rem/0.875rem $font-primary;
  @include transition;
  
  .navbar-brand {
    &.logo-image img {
      max-width: 7.5rem;
      height: auto;
    }
  }
  
  .navbar-toggler {
    padding: 0.5rem 0.625rem 0.5rem 0.625rem;
    background-color: transparent;
    border: none;
    
    .navbar-toggler-awesome {
      position: relative;
      display: block;
      width: 1.125rem;
      height: 0.125rem;
      margin-top: 0.25rem;
      margin-bottom: 0.25rem;
      background-color: $white;
      @include transition;
      
      &.fa-bars {
        top: 0;
        @include transition;
      }
      
      &.fa-times {
        top: 0;
        opacity: 0;
        @include transition;
      }
    }
  }
  
  .navbar-collapse {
    .navbar-nav {
      margin-top: 0.75rem;
      margin-bottom: 0.5rem;
      
      .nav-item {
        .nav-link {
          padding: 0.625rem 0.75rem 0.625rem 0.75rem;
          color: $white;
          text-decoration: none;
          @include transition;
          
          &:hover,
          &.active {
            color: $turquoise;
          }
        }
      }
    }
    
    .social-icons {
      display: none;
      
      .fa-stack {
        width: 2em;
        margin-right: 0.25rem;
        font-size: 0.75rem;
        
        &:last-child {
          margin-right: 0;
        }
        
        a {
          color: $white;
          @include transition;
          
          &:hover {
            color: $turquoise;
          }
          
          i.fa-facebook-f {
            color: $white;
          }
          
          i.fa-twitter {
            color: $white;
          }
          
          i.fa-linkedin-in {
            color: $white;
          }
          
          i.fa-instagram {
            color: $white;
          }
        }
      }
    }
  }
  
  &.top-nav-collapse {
    padding: 0.5rem 1.5rem 0.5rem 1.5rem;
    background-color: $primary-color;
    box-shadow: 0 0.0625rem 0.375rem 0 rgba(0, 0, 0, 0.1);
    
    .navbar-brand {
      &.logo-image img {
        max-width: 6.25rem;
        height: auto;
      }
    }
    
    .navbar-collapse {
      .navbar-nav {
        .nav-item {
          .nav-link {
            color: $white;
            
            &:hover,
            &.active {
              color: $turquoise;
            }
          }
        }
      }
    }
  }
}

@include respond-to(md) {
  .navbar {
    .navbar-collapse {
      .social-icons {
        display: inline-block;
        margin-left: 0.5rem;
      }
    }
  }
}
