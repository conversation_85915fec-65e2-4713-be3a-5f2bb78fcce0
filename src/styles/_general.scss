// General Styles
@use 'variables' as *;
@use 'mixins' as *;

body, html {
  width: 100%;
  height: 100%;
  background-color: $primary-color;
}

body, p {
  color: $white;
  font: $font-weight-light 0.9975rem/1.375rem $font-primary;
  line-height: 1.5;
}

.p-large {
  font: $font-weight-light 1rem/1.5rem $font-primary;
}

.p-small {
  font: $font-weight-light 0.75rem/1.25rem $font-primary;
}

.p-heading {
  margin-bottom: $spacing-xl;
}

.li-space-lg li {
  margin-bottom: $spacing-xs;
}

.indent {
  padding-left: 1.25rem;
}

h1 {
  color: $white;
  font: $font-weight-extrabold 3rem/3.5rem $font-primary;
}

h2 {
  color: $white;
  font: $font-weight-semibold 2.25rem/2.75rem $font-primary;
}

h3 {
  color: $white;
  font: $font-weight-bold 1.75rem/2rem $font-primary;
}

h4 {
  color: $turquoise;
  font: $font-weight-medium 1.75rem/1.875rem $font-primary;
}

h5 {
  color: $white;
  font: $font-weight-bold 1.125rem/1.625rem $font-primary;
}

h6 {
  color: $dark-gray;
  font: $font-weight-bold 1rem/1.5rem $font-primary;
}

a {
  color: $light-gray;
  text-decoration: underline;

  &:hover {
    color: $light-gray;
    text-decoration: underline;
  }

  &.turquoise {
    color: $secondary-color;
  }

  &.white {
    color: $white;
  }
}

.testimonial-text {
  font: italic $font-weight-regular 1rem/1.5rem $font-primary;
}

.testimonial-author {
  font: $font-weight-bold 1rem/1.5rem $font-primary;
}

.turquoise {
  color: $turquoise;
}

.btn-solid-reg {
  display: inline-block;
  padding: 1.1875rem 2.125rem 1.1875rem 2.125rem;
  border: 0.125rem solid $turquoise;
  border-radius: 2rem;
  background-color: $turquoise;
  color: $white;
  font: $font-weight-semibold 0.875rem/0 $font-primary;
  text-decoration: none;
  @include transition;

  &:hover {
    background-color: transparent;
    color: $turquoise;
    text-decoration: none;
  }
}

.btn-outline-reg {
  display: inline-block;
  padding: 1.1875rem 2.125rem 1.1875rem 2.125rem;
  border: 0.125rem solid $white;
  border-radius: 2rem;
  background-color: transparent;
  color: $white;
  font: $font-weight-semibold 0.875rem/0 $font-primary;
  text-decoration: none;
  @include transition;

  &:hover {
    background-color: $white;
    color: $primary-color;
    text-decoration: none;
  }
}

.btn-solid-lg {
  display: inline-block;
  padding: 1.375rem 2.625rem 1.375rem 2.625rem;
  border: 0.125rem solid $turquoise;
  border-radius: 2rem;
  background-color: $turquoise;
  color: $white;
  font: $font-weight-bold 0.875rem/0 $font-primary;
  text-decoration: none;
  @include transition;

  &:hover {
    background-color: transparent;
    color: $turquoise;
    text-decoration: none;
  }
}

.btn-outline-lg {
  display: inline-block;
  padding: 1.375rem 2.625rem 1.375rem 2.625rem;
  border: 0.125rem solid $white;
  border-radius: 2rem;
  background-color: transparent;
  color: $white;
  font: $font-weight-bold 0.875rem/0 $font-primary;
  text-decoration: none;
  @include transition;

  &:hover {
    background-color: $white;
    color: $primary-color;
    text-decoration: none;
  }
}

.btn-solid-sm {
  display: inline-block;
  padding: 1rem 1.5rem 1rem 1.5rem;
  border: 0.125rem solid $turquoise;
  border-radius: 2rem;
  background-color: $turquoise;
  color: $white;
  font: $font-weight-bold 0.875rem/0 $font-primary;
  text-decoration: none;
  @include transition;

  &:hover {
    background-color: transparent;
    color: $turquoise;
    text-decoration: none;
  }
}

.btn-outline-sm {
  display: inline-block;
  padding: 1rem 1.5rem 1rem 1.5rem;
  border: 0.125rem solid $white;
  border-radius: 2rem;
  background-color: transparent;
  color: $white;
  font: $font-weight-bold 0.875rem/0 $font-primary;
  text-decoration: none;
  @include transition;

  &:hover {
    background-color: $white;
    color: $primary-color;
    text-decoration: none;
  }
}

.form-group {
  position: relative;
  margin-bottom: 1.25rem;
}

.form-control-input,
.form-control-textarea {
  width: 100%;
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
  padding-left: 1.5rem;
  border: 1px solid $light-gray;
  border-radius: 0.25rem;
  background-color: $white;
  color: $dark-gray;
  font: $font-weight-regular 0.875rem/1.375rem $font-primary;
  @include transition;

  &:focus {
    border: 1px solid $turquoise;
    outline: none;
  }
}

.form-control-textarea {
  display: block;
  height: 14rem;
}

.form-control-submit-button {
  display: inline-block;
  width: 100%;
  height: 3.125rem;
  border: 0.125rem solid $turquoise;
  border-radius: 1.5rem;
  background-color: $turquoise;
  color: $white;
  font: $font-weight-bold 0.875rem/0 $font-primary;
  cursor: pointer;
  @include transition;

  &:hover {
    background-color: transparent;
    color: $turquoise;
  }
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.media {
  display: flex;
  align-items: flex-start;

  .media-body {
    flex: 1;
  }
}

.fas, .fab {
  margin-right: 0.5rem;
  font-size: 0.875rem;
}

img {
  max-width: 100%;
  height: auto;
}
