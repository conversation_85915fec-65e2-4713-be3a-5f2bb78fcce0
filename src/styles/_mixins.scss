// Responsive breakpoints
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: $breakpoint-xs) {
      @content;
    }
  } @else if $breakpoint == sm {
    @media (max-width: $breakpoint-sm) {
      @content;
    }
  } @else if $breakpoint == md {
    @media (max-width: $breakpoint-md) {
      @content;
    }
  } @else if $breakpoint == lg {
    @media (max-width: $breakpoint-lg) {
      @content;
    }
  }
}

// Flexbox
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// Transitions
@mixin transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

// Buttons
@mixin button-style($bg-color, $text-color, $hover-bg-color, $hover-text-color) {
  background-color: $bg-color;
  color: $text-color;
  @include transition;
  
  &:hover {
    background-color: $hover-bg-color;
    color: $hover-text-color;
  }
}

// Box shadow
@mixin box-shadow($x: 0, $y: 2px, $blur: 10px, $spread: 0, $color: rgba(0, 0, 0, 0.1)) {
  box-shadow: $x $y $blur $spread $color;
}
