/*
 * ResearchSat - Main Stylesheet
 * This file contains all custom styles for the ResearchSat website
 * Consolidated from multiple files for better performance
 */

/******************************/
/*     01. General Styles     */
/******************************/

body, html {
  width: 100%;
  height: 100%;
  background-color: #121212;
}

body, p {
  color: white;
  font: 300 0.9975rem/1.375rem "Poppins", sans-serif;
  line-height: 1.5;
}

.p-large {
  font: 300 1rem/1.5rem "Poppins", sans-serif;
}

.p-small {
  font: 300 0.75rem/1.25rem "Poppins", sans-serif;
}

.p-heading {
  margin-bottom: 3.875rem;
}

.li-space-lg li {
  margin-bottom: 0.25rem;
}

.indent {
  padding-left: 1.25rem;
}

h1 {
  color: white;
  font: 700 3rem/3.5rem "Poppins", sans-serif;
}

h2 {
  color: white;
  font: 600 2.25rem/2.75rem "Poppins", sans-serif;
}

h3 {
  color: white;
  font: 600 1.75rem/2rem "Poppins", sans-serif;
}

h4 {
  color: #0E7369;
  font: 500 1.75rem/1.875rem "Poppins", sans-serif;
}

h5 {
  color: white;
  font: 600 1.125rem/1.625rem "Poppins", sans-serif;
}

h6 {
  color: #393939;
  font: 600 1rem/1.5rem "Poppins", sans-serif;
}

a {
  color: #626262;
  text-decoration: underline;
}

a:hover {
  color: #626262;
  text-decoration: underline;
}

a.turquoise {
  color: #5f7371;
}

a.white {
  color: #fff;
}

.testimonial-text {
  font: italic 400 1rem/1.5rem "Montserrat", sans-serif;
}

.testimonial-author {
  font: 700 1rem/1.5rem "Montserrat", sans-serif;
}

.turquoise {
  color: #0E7369;
}

.btn-solid-reg {
  display: inline-block;
  padding: 1.1875rem 2.125rem 1.1875rem 2.125rem;
  border: 0.125rem solid #0E7369;
  border-radius: 2rem;
  background-color: #0E7369;
  color: white;
  font: 600 0.875rem/0 "Montserrat", sans-serif;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-solid-reg:hover {
  background-color: transparent;
  color: #0E7369;
  text-decoration: none;
}

.btn-outline-reg {
  display: inline-block;
  padding: 1.1875rem 2.125rem 1.1875rem 2.125rem;
  border: 0.125rem solid white;
  border-radius: 2rem;
  background-color: transparent;
  color: white;
  font: 600 0.875rem/0 "Montserrat", sans-serif;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-outline-reg:hover {
  background-color: white;
  color: #17242D;
  text-decoration: none;
}

/* Preloader */
.spinner-wrapper {
  position: fixed;
  z-index: 999999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #121212;
  opacity: 1;
  transition: opacity 0.5s ease;
}

.spinner-wrapper .spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 3.75rem;
  height: 3.75rem;
  transform: translate(-50%, -50%);
}

/* SVG elements styling */
.cls-1 {
  fill: #677d7c;
  stroke: #677d7c;
  animation: pulse 2s infinite alternate;
}

.cls-2 {
  fill: #ff3241;
  stroke: #ff3241;
  animation: pulse 2s infinite alternate;
  animation-delay: 0.5s;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* Navigation */
.navbar {
  background-color: transparent;
  font: 600 0.875rem/0.875rem "Montserrat", sans-serif;
  transition: all 0.3s ease;
}

.navbar .navbar-brand.logo-image img {
  max-width: 7.5rem;
  height: auto;
}

.navbar .navbar-toggler {
  padding: 0.5rem 0.625rem 0.5rem 0.625rem;
  background-color: transparent;
  border: none;
}

.navbar .navbar-toggler .navbar-toggler-awesome {
  position: relative;
  display: block;
  width: 1.125rem;
  height: 0.125rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  background-color: white;
  transition: all 0.3s ease;
}

.navbar .navbar-toggler .navbar-toggler-awesome.fa-bars {
  top: 0;
  transition: all 0.3s ease;
}

.navbar .navbar-toggler .navbar-toggler-awesome.fa-times {
  top: 0;
  opacity: 0;
  transition: all 0.3s ease;
}

.navbar .navbar-collapse .navbar-nav {
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

.navbar .navbar-collapse .navbar-nav .nav-item .nav-link {
  padding: 0.625rem 0.75rem 0.625rem 0.75rem;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.navbar .navbar-collapse .navbar-nav .nav-item .nav-link:hover,
.navbar .navbar-collapse .navbar-nav .nav-item .nav-link.active {
  color: #0E7369;
}

.navbar .navbar-collapse .social-icons {
  display: none;
}

.navbar .navbar-collapse .social-icons .fa-stack {
  width: 2em;
  margin-right: 0.25rem;
  font-size: 0.75rem;
}

.navbar .navbar-collapse .social-icons .fa-stack:last-child {
  margin-right: 0;
}

.navbar .navbar-collapse .social-icons .fa-stack a {
  color: white;
  transition: all 0.3s ease;
}

.navbar .navbar-collapse .social-icons .fa-stack a:hover {
  color: #0E7369;
}

.navbar .navbar-collapse .social-icons .fa-stack a i.fa-facebook-f,
.navbar .navbar-collapse .social-icons .fa-stack a i.fa-twitter,
.navbar .navbar-collapse .social-icons .fa-stack a i.fa-linkedin-in,
.navbar .navbar-collapse .social-icons .fa-stack a i.fa-instagram {
  color: white;
}

.navbar.top-nav-collapse {
  padding: 0.5rem 1.5rem 0.5rem 1.5rem;
  background-color: #121212;
  box-shadow: 0 0.0625rem 0.375rem 0 rgba(0, 0, 0, 0.1);
}

.navbar.top-nav-collapse .navbar-brand.logo-image img {
  max-width: 6.25rem;
  height: auto;
}

.navbar.top-nav-collapse .navbar-collapse .navbar-nav .nav-item .nav-link {
  color: white;
}

.navbar.top-nav-collapse .navbar-collapse .navbar-nav .nav-item .nav-link:hover,
.navbar.top-nav-collapse .navbar-collapse .navbar-nav .nav-item .nav-link.active {
  color: #0E7369;
}

/* Header */
.header {
  position: relative;
  padding-top: 8rem;
  padding-bottom: 4rem;
  background-color: #121212;
  text-align: center;
}

.header .header-content {
  padding-top: 4rem;
  padding-bottom: 2rem;
}

.header .header-content .text-container {
  margin-bottom: 3rem;
}

.header .header-content .text-container h4 {
  margin-bottom: 1.5rem;
}

.header .header-content .text-container h2 {
  margin-bottom: 1.5rem;
}

.header .header-content .text-container .p-large {
  margin-bottom: 2rem;
}

.header .header-content .image-container {
  margin-bottom: 3rem;
}

.header .header-content .image-container img {
  max-width: 100%;
  height: auto;
}

.header #partners-logos {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}

.header #partners-logos img {
  max-height: 2.5rem;
  margin-right: 1.5rem;
  margin-bottom: 1rem;
}

/* Footer */
.footer {
  padding-top: 4.5rem;
  padding-bottom: 4rem;
  background-color: #121212;
}

.footer .footer-col {
  margin-bottom: 2rem;
}

.footer .footer-col.first {
  margin-right: 1.5rem;
}

.footer .footer-col img {
  max-width: 100%;
  height: auto;
  margin-bottom: 1.5rem;
}

.footer .footer-col h4 {
  margin-bottom: 1.5rem;
}

.footer .footer-col p {
  margin-bottom: 1.5rem;
}

.footer .footer-col .list-unstyled .media {
  margin-bottom: 0.5rem;
}

.footer .footer-col .list-unstyled .media i {
  margin-right: 0.5rem;
  color: #0E7369;
}

.footer .footer-col.last {
  margin-right: 0;
}

.footer .footer-col.last .fa-stack {
  width: 2em;
  margin-bottom: 0.75rem;
  margin-right: 0.375rem;
  font-size: 1.5rem;
}

.footer .footer-col.last .fa-stack:last-child {
  margin-right: 0;
}

.footer .footer-col.last .fa-stack a {
  color: white;
  transition: all 0.3s ease;
}

.footer .footer-col.last .fa-stack a:hover {
  color: #0E7369;
}

.footer .footer-col.last .fa-stack a i.fa-circle {
  color: #393939;
  transition: all 0.3s ease;
}

.footer .footer-col.last .fa-stack a i.fa-stack-1x {
  color: white;
  transition: all 0.3s ease;
}

.footer .footer-col.last .fa-stack a:hover i.fa-circle {
  color: #0E7369;
}

.copyright {
  padding-top: 1.75rem;
  padding-bottom: 0.75rem;
  background-color: #111a21;
  text-align: center;
}

.copyright .p-small {
  margin-bottom: 0;
  color: white;
}

.copyright .p-small a {
  color: white;
  text-decoration: none;
}

.copyright .p-small a:hover {
  color: #0E7369;
}

/* Media Queries */
@media (max-width: 576px) {
  .header {
    padding-top: 6rem;
    padding-bottom: 3rem;
  }

  .header .header-content {
    padding-top: 2rem;
    padding-bottom: 1rem;
  }

  .header .header-content .text-container h4 {
    font-size: 1.2em;
  }

  .header .header-content .text-container h2 {
    font-size: 1.8rem;
  }

  .header .header-content .text-container .p-large {
    font-size: 1rem;
  }

  .header #partners-logos img {
    max-height: 2rem;
    margin-right: 1rem;
  }
}

@media (max-width: 768px) {
  .navbar .navbar-brand.logo-image img {
    max-width: 6.5rem;
  }

  .header .header-content .text-container h4 {
    font-size: 1.3em;
  }

  .header .header-content .text-container h2 {
    font-size: 2rem;
  }
}

@media (min-width: 768px) {
  .navbar {
    padding: 1.75rem 1.5rem 1.75rem 1.5rem;
  }

  .navbar .navbar-brand.logo-image img {
    max-width: 7rem;
  }

  .navbar .navbar-collapse .navbar-nav {
    margin-top: 0;
    margin-bottom: 0;
  }

  .navbar .navbar-collapse .navbar-nav .nav-item {
    margin-right: 0.75rem;
    margin-left: 0.75rem;
  }

  .navbar .navbar-collapse .navbar-nav .nav-item:last-child {
    margin-right: 0;
  }

  .navbar .navbar-collapse .social-icons {
    display: inline-block;
    margin-left: 0.5rem;
  }

  .header {
    padding-top: 9rem;
    padding-bottom: 8rem;
    text-align: left;
  }

  .header .header-content .text-container {
    margin-top: 3rem;
    margin-bottom: 0;
  }

  .footer .footer-col {
    margin-bottom: 2rem;
  }

  .footer .footer-col.middle {
    margin-bottom: 0;
  }

  .footer .footer-col.last {
    margin-bottom: 0;
  }
}

@media (min-width: 992px) {
  .navbar {
    padding: 2.125rem 1.5rem 2.125rem 1.5rem;
  }

  .navbar .navbar-brand.logo-image img {
    max-width: 7.5rem;
  }

  .header {
    padding-top: 11rem;
    padding-bottom: 9rem;
  }

  .header .header-content .text-container {
    margin-top: 4rem;
  }
}
