// Media Queries

// Extra small devices (portrait phones, less than 576px)
@include respond-to(xs) {
  .header {
    padding-top: 6rem;
    padding-bottom: 3rem;
    
    .header-content {
      padding-top: 2rem;
      padding-bottom: 1rem;
      
      .text-container {
        h4 {
          font-size: 1.2em;
        }
        
        h2 {
          font-size: 1.8rem;
        }
        
        .p-large {
          font-size: 1rem;
        }
      }
    }
    
    #partners-logos {
      img {
        max-height: 2rem;
        margin-right: 1rem;
      }
    }
  }
  
  .basic-1, .basic-2 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  
  #benefits {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  
  .form-2 {
    padding-top: 5rem;
    padding-bottom: 4rem;
  }
  
  .footer {
    padding-top: 3rem;
    padding-bottom: 2.5rem;
  }
}

// Small devices (landscape phones, 576px and up)
@include respond-to(sm) {
  .navbar {
    .navbar-brand {
      &.logo-image img {
        max-width: 6.5rem;
      }
    }
  }
  
  .header {
    .header-content {
      .text-container {
        h4 {
          font-size: 1.3em;
        }
        
        h2 {
          font-size: 2rem;
        }
      }
    }
  }
}

// Medium devices (tablets, 768px and up)
@include respond-to(md) {
  .navbar {
    padding: 1.75rem 1.5rem 1.75rem 1.5rem;
    
    .navbar-brand {
      &.logo-image img {
        max-width: 7rem;
      }
    }
    
    .navbar-collapse {
      .navbar-nav {
        margin-top: 0;
        margin-bottom: 0;
        
        .nav-item {
          margin-right: 0.75rem;
          margin-left: 0.75rem;
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
  
  .header {
    padding-top: 9rem;
    padding-bottom: 8rem;
    text-align: left;
    
    .header-content {
      .text-container {
        margin-top: 3rem;
        margin-bottom: 0;
      }
    }
  }
  
  .basic-1, .basic-2 {
    .text-container {
      margin-bottom: 0;
    }
    
    .image-container {
      margin-bottom: 0;
    }
  }
  
  #benefits {
    #forResearchers, #forIndustry {
      .basic-1, .basic-2 {
        .text-container {
          margin-bottom: 0;
        }
        
        .image-container {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .footer {
    .footer-col {
      margin-bottom: 2rem;
      
      &.middle {
        margin-bottom: 0;
      }
      
      &.last {
        margin-bottom: 0;
      }
    }
  }
}

// Large devices (desktops, 992px and up)
@include respond-to(lg) {
  .navbar {
    padding: 2.125rem 1.5rem 2.125rem 1.5rem;
    
    .navbar-brand {
      &.logo-image img {
        max-width: 7.5rem;
      }
    }
  }
  
  .header {
    padding-top: 11rem;
    padding-bottom: 9rem;
    
    .header-content {
      .text-container {
        margin-top: 4rem;
      }
    }
  }
}
