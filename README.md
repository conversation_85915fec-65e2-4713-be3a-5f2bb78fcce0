# ResearchSat Website - Vite Version

This is a modern recreation of the ResearchSat website using Vite, React, and modern web development practices.

## Deployment

The website can be deployed on any static hosting service. The build output is in the `dist` directory.

## Features

- Built with Vite for fast development and optimized production builds
- React components for better code organization and reusability
- SCSS for enhanced styling capabilities
- Responsive design for all device sizes
- Modern JavaScript with ES6+ features
- Optimized asset loading

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm 8.x or later

### Installation

1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:

```bash
npm install
```

### Development

To start the development server:

```bash
npm run dev
```

### Building for Production

To build the project for production:

```bash
npm run build
```

### Preview Production Build

To preview the production build locally:

```bash
npm run preview
```

## Project Structure

- `src/` - Source files
  - `assets/` - Static assets (images, fonts, etc.)
  - `components/` - React components
  - `pages/` - Page components
  - `scripts/` - JavaScript utilities
  - `styles/` - SCSS stylesheets
- `public/` - Public static files

## License

This project is licensed under the ISC License.
