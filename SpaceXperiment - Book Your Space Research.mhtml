From: <Saved by Blink>
Snapshot-Content-Location: https://6000-firebase-studio-1746831357630.cluster-xpmcxs2fjnhg6xvn446ubtgpio.cloudworkstations.dev/
Subject: SpaceXperiment - Book Your Space Research
Date: Thu, 29 May 2025 13:59:59 +0930
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--yq6765fiDXY66DPzXDja0aomfPDoeACPgDwEvJsm9d----"


------MultipartBoundary--yq6765fiDXY66DPzXDja0aomfPDoeACPgDwEvJsm9d----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://6000-firebase-studio-1746831357630.cluster-xpmcxs2fjnhg6xvn446ubtgpio.cloudworkstations.dev/

<!DOCTYPE html><html lang=3D"en"><head><meta http-equiv=3D"Content-Type" co=
ntent=3D"text/html; charset=3DUTF-8"><link rel=3D"stylesheet" type=3D"text/=
css" href=3D"cid:<EMAIL>" /><m=
eta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=3D1"><=
link rel=3D"stylesheet" href=3D"https://6000-firebase-studio-1746831357630.=
cluster-xpmcxs2fjnhg6xvn446ubtgpio.cloudworkstations.dev/_next/static/chunk=
s/%5Broot%20of%20the%20server%5D__b07dbc43._.css" data-precedence=3D"next_s=
tatic/chunks/[root of the server]__b07dbc43._.css"><link rel=3D"preload" as=
=3D"script" fetchpriority=3D"low" href=3D"https://6000-firebase-studio-1746=
831357630.cluster-xpmcxs2fjnhg6xvn446ubtgpio.cloudworkstations.dev/_next/st=
atic/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_49a6ea35._=
.js"><meta name=3D"next-size-adjust" content=3D""><title>SpaceXperiment - B=
ook Your Space Research</title><meta name=3D"description" content=3D"Intera=
ctive booking for space experiments with SpaceXperiment."><link rel=3D"icon=
" href=3D"https://6000-firebase-studio-1746831357630.cluster-xpmcxs2fjnhg6x=
vn446ubtgpio.cloudworkstations.dev/favicon.ico?favicon.56766c03.ico" sizes=
=3D"48x48" type=3D"image/x-icon"><link rel=3D"preload" href=3D"https://6000=
-firebase-studio-1746831357630.cluster-xpmcxs2fjnhg6xvn446ubtgpio.cloudwork=
stations.dev/_next/static/media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2=
" as=3D"font" crossorigin=3D"" type=3D"font/woff2"><link rel=3D"preload" hr=
ef=3D"https://6000-firebase-studio-1746831357630.cluster-xpmcxs2fjnhg6xvn44=
6ubtgpio.cloudworkstations.dev/_next/static/media/or3nQ6H_1_WfwkMZI_qYFrcdm=
hHkjko-s.p.be19f591.woff2" as=3D"font" crossorigin=3D"" type=3D"font/woff2"=
></head><body class=3D"geist_e531dabc-module__QGiZLq__variable geist_mono_6=
8a01160-module__YLcDdW__variable antialiased bg-background text-foreground =
min-h-screen flex flex-col"><main class=3D"flex-grow"><div class=3D"contain=
er mx-auto px-4 py-8 sm:px-6 lg:px-8"><header class=3D"mb-12 text-center"><=
div class=3D"flex items-center justify-center text-3xl font-bold"><svg xmln=
s=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 =
24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-line=
cap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-rocket h-8 w=
-8 mr-2 text-primary"><path d=3D"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.7=
1-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path><path d=3D"m12 15-3=
-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.3=
5 0 0 1-4 2z"></path><path d=3D"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></p=
ath><path d=3D"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path></svg><span =
class=3D"text-foreground">Space</span><span class=3D"text-primary">X</span>=
<span class=3D"text-foreground">periment</span><svg xmlns=3D"http://www.w3.=
org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none=
" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" strok=
e-linejoin=3D"round" class=3D"lucide lucide-flask-conical h-8 w-8 ml-2 text=
-accent"><path d=3D"M14 2v6a2 2 0 0 0 .245.96l5.51 10.08A2 2 0 0 1 18 22H6a=
2 2 0 0 1-1.755-2.96l5.51-10.08A2 2 0 0 0 10 8V2"></path><path d=3D"M6.453 =
15h11.094"></path><path d=3D"M8.5 2h7"></path></svg></div><h1 class=3D"mt-4=
 text-3xl font-extrabold tracking-tight sm:text-4xl lg:text-5xl">Launch You=
r Research into Orbit</h1><p class=3D"mt-4 max-w-3xl mx-auto text-lg text-m=
uted-foreground sm:text-xl">Welcome to SpaceXperiment! Follow these simple =
steps to select a package, configure your experiment parameters, and submit=
 your research mission request.</p></header><div class=3D"mb-10"><nav aria-=
label=3D"Progress"><ol role=3D"list" class=3D"flex items-center justify-cen=
ter space-x-2 sm:space-x-4"><li class=3D"flex-1"><div class=3D"group flex f=
lex-col items-center py-2 transition-colors duration-300 ease-in-out text-p=
rimary"><div class=3D"flex items-center w-full"><div class=3D"flex-grow bor=
der-t-2 transition-colors duration-300" style=3D"visibility:hidden"></div><=
div class=3D"flex flex-col items-center mx-2"><span class=3D"flex h-10 w-10=
 items-center justify-center rounded-full border-2 transition-all duration-=
300 ease-in-out border-primary ring-4 ring-primary/30 bg-background"><svg x=
mlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0=
 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-l=
inecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-package2 =
h-5 w-5"><path d=3D"M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path><pa=
th d=3D"m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>=
<path d=3D"M12 3v6"></path></svg></span></div><div class=3D"flex-grow borde=
r-t-2 transition-colors duration-300 border-border" style=3D"visibility:vis=
ible"></div></div><span class=3D"mt-2 text-xs sm:text-sm font-medium text-c=
enter text-primary">Package</span></div></li><li class=3D"flex-1"><div clas=
s=3D"group flex flex-col items-center py-2 transition-colors duration-300 e=
ase-in-out text-muted-foreground hover:text-foreground"><div class=3D"flex =
items-center w-full"><div class=3D"flex-grow border-t-2 transition-colors d=
uration-300 border-border" style=3D"visibility:visible"></div><div class=3D=
"flex flex-col items-center mx-2"><span class=3D"flex h-10 w-10 items-cente=
r justify-center rounded-full border-2 transition-all duration-300 ease-in-=
out border-border group-hover:border-foreground"><svg xmlns=3D"http://www.w=
3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"no=
ne" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" str=
oke-linejoin=3D"round" class=3D"lucide lucide-flask-conical h-5 w-5"><path =
d=3D"M14 2v6a2 2 0 0 0 .245.96l5.51 10.08A2 2 0 0 1 18 22H6a2 2 0 0 1-1.755=
-2.96l5.51-10.08A2 2 0 0 0 10 8V2"></path><path d=3D"M6.453 15h11.094"></pa=
th><path d=3D"M8.5 2h7"></path></svg></span></div><div class=3D"flex-grow b=
order-t-2 transition-colors duration-300 border-border" style=3D"visibility=
:visible"></div></div><span class=3D"mt-2 text-xs sm:text-sm font-medium te=
xt-center">Experiment</span></div></li><li class=3D"flex-1"><div class=3D"g=
roup flex flex-col items-center py-2 transition-colors duration-300 ease-in=
-out text-muted-foreground hover:text-foreground"><div class=3D"flex items-=
center w-full"><div class=3D"flex-grow border-t-2 transition-colors duratio=
n-300 border-border" style=3D"visibility:visible"></div><div class=3D"flex =
flex-col items-center mx-2"><span class=3D"flex h-10 w-10 items-center just=
ify-center rounded-full border-2 transition-all duration-300 ease-in-out bo=
rder-border group-hover:border-foreground"><svg xmlns=3D"http://www.w3.org/=
2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" st=
roke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-li=
nejoin=3D"round" class=3D"lucide lucide-send h-5 w-5"><path d=3D"M14.536 21=
.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 =
0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"></path><path d=3D"m21.854 2.147=
-10.94 10.939"></path></svg></span></div><div class=3D"flex-grow border-t-2=
 transition-colors duration-300" style=3D"visibility:hidden"></div></div><s=
pan class=3D"mt-2 text-xs sm:text-sm font-medium text-center">Submit</span>=
</div></li></ol></nav></div><div class=3D"flex justify-center w-full"><div =
class=3D"w-full lg:max-w-4xl xl:max-w-5xl"><div class=3D"space-y-10"><div c=
lass=3D"space-y-8"><h2 class=3D"text-2xl font-semibold text-center tracking=
-tight mb-6">Choose Your Mission Package</h2><div class=3D"grid grid-cols-1=
 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class=3D"rounded-lg border bg-ca=
rd text-card-foreground flex flex-col transition-all duration-300 ease-in-o=
ut shadow-lg hover:shadow-xl border-border"><div class=3D"flex flex-col spa=
ce-y-1.5 p-6 items-center pb-4"><svg xmlns=3D"http://www.w3.org/2000/svg" w=
idth=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"cur=
rentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"r=
ound" class=3D"lucide lucide-lightbulb h-10 w-10 mb-3 text-accent"><path d=
=3D"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.=
2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"></path><path d=3D"M9 18h6"></path><path d=3D=
"M10 22h4"></path></svg><div class=3D"font-semibold tracking-tight text-2xl=
">Startup Spark</div><div class=3D"text-sm text-muted-foreground text-cente=
r h-12">Ideal for new ventures, academic labs, or preliminary research proj=
ects looking for a cost-effective way to access space.</div></div><div clas=
s=3D"p-6 pt-0 flex-grow"><div class=3D"w-full" data-orientation=3D"vertical=
"><div data-state=3D"closed" data-orientation=3D"vertical" class=3D"border-=
b"><h3 data-orientation=3D"vertical" data-state=3D"closed" class=3D"flex"><=
button type=3D"button" aria-controls=3D"radix-=C2=ABRbcstl7=C2=BB" aria-exp=
anded=3D"false" data-state=3D"closed" data-orientation=3D"vertical" id=3D"r=
adix-=C2=ABR3cstl7=C2=BB" class=3D"flex flex-1 items-center justify-between=
 py-4 transition-all [&amp;[data-state=3Dopen]>svg]:rotate-180 text-sm font=
-semibold hover:no-underline" data-radix-collection-item=3D"">View Features=
 (<!-- -->5<!-- -->)<svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" =
height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" s=
troke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-chevron-down h-4 w-4 shrink-0 transition-transform durati=
on-200"><path d=3D"m6 9 6 6 6-6"></path></svg></button></h3></div></div></d=
iv><div class=3D"flex items-center p-6 pt-0"><button class=3D"inline-flex i=
tems-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-=
medium ring-offset-background focus-visible:outline-none focus-visible:ring=
-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-eve=
nts-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:si=
ze-4 [&amp;_svg]:shrink-0 border border-input bg-background hover:bg-accent=
 hover:text-accent-foreground h-10 px-4 py-2 w-full transition-transform du=
ration-200 hover:scale-105" aria-pressed=3D"false"><svg xmlns=3D"http://www=
.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"=
none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" s=
troke-linejoin=3D"round" class=3D"lucide lucide-lightbulb mr-2 h-4 w-4"><pa=
th d=3D"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .=
2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"></path><path d=3D"M9 18h6"></path><path =
d=3D"M10 22h4"></path></svg>Select Spark</button></div></div><div class=3D"=
rounded-lg border bg-card text-card-foreground flex flex-col transition-all=
 duration-300 ease-in-out shadow-lg hover:shadow-xl border-accent ring-2 ri=
ng-accent"><div class=3D"py-1.5 px-4 bg-accent text-accent-foreground text-=
sm font-semibold text-center rounded-t-md">Most Popular</div><div class=3D"=
flex flex-col space-y-1.5 p-6 items-center pb-4"><svg xmlns=3D"http://www.w=
3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"no=
ne" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" str=
oke-linejoin=3D"round" class=3D"lucide lucide-rocket h-10 w-10 mb-3 text-pr=
imary"><path d=3D"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.0=
9-2.91a2.18 2.18 0 0 0-2.91-.09z"></path><path d=3D"m12 15-3-3a22 22 0 0 1 =
2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"><=
/path><path d=3D"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path><path d=3D"=
M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path></svg><div class=3D"font-se=
mibold tracking-tight text-2xl">Growth Galaxy</div><div class=3D"text-sm te=
xt-muted-foreground text-center h-12">For established researchers or compan=
ies scaling their R&amp;D, offering a balance of resources and support for =
more complex experimental needs.</div></div><div class=3D"p-6 pt-0 flex-gro=
w"><div class=3D"w-full" data-orientation=3D"vertical"><div data-state=3D"c=
losed" data-orientation=3D"vertical" class=3D"border-b"><h3 data-orientatio=
n=3D"vertical" data-state=3D"closed" class=3D"flex"><button type=3D"button"=
 aria-controls=3D"radix-=C2=ABRbkstl7=C2=BB" aria-expanded=3D"false" data-s=
tate=3D"closed" data-orientation=3D"vertical" id=3D"radix-=C2=ABR3kstl7=C2=
=BB" class=3D"flex flex-1 items-center justify-between py-4 transition-all =
[&amp;[data-state=3Dopen]>svg]:rotate-180 text-sm font-semibold hover:no-un=
derline" data-radix-collection-item=3D"">View Features (<!-- -->7<!-- -->)<=
svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-chev=
ron-down h-4 w-4 shrink-0 transition-transform duration-200"><path d=3D"m6 =
9 6 6 6-6"></path></svg></button></h3></div></div></div><div class=3D"flex =
items-center p-6 pt-0"><button class=3D"inline-flex items-center justify-ce=
nter gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-bac=
kground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-=
ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opac=
ity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrin=
k-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w=
-full transition-transform duration-200 hover:scale-105" aria-pressed=3D"fa=
lse"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" v=
iewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"=
2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucid=
e-rocket mr-2 h-4 w-4"><path d=3D"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.=
71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path><path d=3D"m12 15-=
3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.=
35 0 0 1-4 2z"></path><path d=3D"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></=
path><path d=3D"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path></svg>Selec=
t Galaxy</button></div></div><div class=3D"rounded-lg border bg-card text-c=
ard-foreground flex flex-col transition-all duration-300 ease-in-out shadow=
-lg hover:shadow-xl border-border"><div class=3D"flex flex-col space-y-1.5 =
p-6 items-center pb-4"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"2=
4" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor=
" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" cla=
ss=3D"lucide lucide-workflow h-10 w-10 mb-3 text-accent"><rect width=3D"8" =
height=3D"8" x=3D"3" y=3D"3" rx=3D"2"></rect><path d=3D"M7 11v4a2 2 0 0 0 2=
 2h4"></path><rect width=3D"8" height=3D"8" x=3D"13" y=3D"13" rx=3D"2"></re=
ct></svg><div class=3D"font-semibold tracking-tight text-2xl">Enterprise Co=
smos</div><div class=3D"text-sm text-muted-foreground text-center h-12">Com=
prehensive, fully tailored solutions for large-scale, highly specific, or m=
ission-critical research programs requiring maximum flexibility and dedicat=
ed support.</div></div><div class=3D"p-6 pt-0 flex-grow"><div class=3D"w-fu=
ll" data-orientation=3D"vertical"><div data-state=3D"closed" data-orientati=
on=3D"vertical" class=3D"border-b"><h3 data-orientation=3D"vertical" data-s=
tate=3D"closed" class=3D"flex"><button type=3D"button" aria-controls=3D"rad=
ix-=C2=ABRbsstl7=C2=BB" aria-expanded=3D"false" data-state=3D"closed" data-=
orientation=3D"vertical" id=3D"radix-=C2=ABR3sstl7=C2=BB" class=3D"flex fle=
x-1 items-center justify-between py-4 transition-all [&amp;[data-state=3Dop=
en]>svg]:rotate-180 text-sm font-semibold hover:no-underline" data-radix-co=
llection-item=3D"">View Features (<!-- -->8<!-- -->)<svg xmlns=3D"http://ww=
w.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D=
"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" =
stroke-linejoin=3D"round" class=3D"lucide lucide-chevron-down h-4 w-4 shrin=
k-0 transition-transform duration-200"><path d=3D"m6 9 6 6 6-6"></path></sv=
g></button></h3></div></div></div><div class=3D"flex items-center p-6 pt-0"=
><button class=3D"inline-flex items-center justify-center gap-2 whitespace-=
nowrap rounded-md text-sm font-medium ring-offset-background focus-visible:=
outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:rin=
g-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:poi=
nter-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-inpu=
t bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2=
 w-full transition-transform duration-200 hover:scale-105" aria-pressed=3D"=
false"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24"=
 viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-workflow mr-2 h-4 w-4"><rect width=3D"8" height=3D"8" x=3D"3" y=3D"3"=
 rx=3D"2"></rect><path d=3D"M7 11v4a2 2 0 0 0 2 2h4"></path><rect width=3D"=
8" height=3D"8" x=3D"13" y=3D"13" rx=3D"2"></rect></svg>Contact Sales</butt=
on></div></div></div></div><div class=3D"mt-12 flex flex-col sm:flex-row ju=
stify-between items-center p-6 bg-card border rounded-lg shadow-md gap-4"><=
button class=3D"inline-flex items-center justify-center gap-2 whitespace-no=
wrap rounded-md text-sm font-medium ring-offset-background focus-visible:ou=
tline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-=
offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:point=
er-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input =
bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 t=
ransition-transform duration-200 hover:scale-105 w-full sm:w-auto" disabled=
=3D"" aria-label=3D"Previous Step"><svg xmlns=3D"http://www.w3.org/2000/svg=
" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"=
currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=
=3D"round" class=3D"lucide lucide-arrow-left h-4 w-4 mr-2"><path d=3D"m12 1=
9-7-7 7-7"></path><path d=3D"M19 12H5"></path></svg>Previous</button><butto=
n class=3D"inline-flex items-center justify-center gap-2 whitespace-nowrap =
rounded-md text-sm font-medium ring-offset-background focus-visible:outline=
-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offse=
t-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-ev=
ents-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-f=
oreground hover:bg-primary/90 h-10 px-4 py-2 transition-transform duration-=
200 hover:scale-105 w-full sm:w-auto" aria-label=3D"Next Step">Next <svg xm=
lns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 =
0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-li=
necap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-arrow-righ=
t h-4 w-4 ml-2"><path d=3D"M5 12h14"></path><path d=3D"m12 5 7 7-7 7"></pat=
h></svg></button></div></div></div></div></div><!--$--><!--/$--><!--$--><!-=
-/$--></main><div role=3D"region" aria-label=3D"Notifications (F8)" tabinde=
x=3D"-1" style=3D"pointer-events:none"><ol tabindex=3D"-1" class=3D"fixed t=
op-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:r=
ight-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><nextjs-portal>=
<template shadowmode=3D"open"><div data-nextjs-toast=3D"true" class=3D"next=
js-toast" style=3D"--animate-out-duration-ms: 200ms; --animate-out-timing-f=
unction: cubic-bezier(0.175, 0.885, 0.32, 1.1); box-shadow: none; z-index: =
2147483647; bottom: 20px; left: 20px;"><div data-nextjs-toast-wrapper=3D"tr=
ue"><div data-next-badge-root=3D"true" style=3D"--size: 2.25rem; --duration=
-short: 150ms; display: none;"><div data-next-badge=3D"true" data-error=3D"=
false" data-error-expanded=3D"true" data-animate=3D"false" style=3D"width: =
2.25rem;"><div><div data-issues=3D"true"><button data-issues-open=3D"true" =
aria-label=3D"Open issues overlay"><div data-disabled-icon=3D"true"><svg wi=
dth=3D"12" height=3D"12" viewBox=3D"0 0 12 12" fill=3D"none" xmlns=3D"http:=
//www.w3.org/2000/svg"><path fill-rule=3D"evenodd" clip-rule=3D"evenodd" d=
=3D"M3.98071 1.125L1.125 3.98071L1.125 8.01929L3.98071 10.875H8.01929L10.87=
5 8.01929V3.98071L8.01929 1.125H3.98071ZM3.82538 0C3.62647 0 3.4357 0.07901=
76 3.29505 0.21967L0.21967 3.29505C0.0790176 3.4357 0 3.62647 0 3.82538V8.1=
7462C0 8.37353 0.0790178 8.5643 0.21967 8.70495L3.29505 11.7803C3.4357 11.9=
21 3.62647 12 3.82538 12H8.17462C8.37353 12 8.5643 11.921 8.70495 11.7803L1=
1.7803 8.70495C11.921 8.5643 12 8.37353 12 8.17462V3.82538C12 3.62647 11.92=
1 3.4357 11.7803 3.29505L8.70495 0.21967C8.5643 0.0790177 8.37353 0 8.17462=
 0H3.82538ZM6.5625 2.8125V3.375V6V6.5625H5.4375V6V3.375V2.8125H6.5625ZM6 9C=
6.41421 9 6.75 8.66421 6.75 8.25C6.75 7.83579 6.41421 7.5 6 7.5C5.58579 7.5=
 5.25 7.83579 5.25 8.25C5.25 8.66421 5.58579 9 6 9Z" fill=3D"#EAEAEA"></pat=
h></svg></div><div data-issues-count-animation=3D"true" data-animate=3D"fal=
se"><div aria-hidden=3D"true" data-issues-count-exit=3D"true">-1</div><div =
data-issues-count=3D"true" data-issues-count-enter=3D"true">0</div></div> <=
div>Issue</div></button></div></div></div><div aria-hidden=3D"true" data-do=
t=3D"true"></div></div></div></div></template></nextjs-portal><next-route-a=
nnouncer style=3D"position: absolute;"><template shadowmode=3D"open"><div a=
ria-live=3D"assertive" id=3D"__next-route-announcer__" role=3D"alert" style=
=3D"position: absolute; border: 0px; height: 1px; margin: -1px; padding: 0p=
x; width: 1px; clip: rect(0px, 0px, 0px, 0px); overflow: hidden; white-spac=
e: nowrap; overflow-wrap: normal;"></div></template></next-route-announcer>=
</body></html>
------MultipartBoundary--yq6765fiDXY66DPzXDja0aomfPDoeACPgDwEvJsm9d----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://6000-firebase-studio-1746831357630.cluster-xpmcxs2fjnhg6xvn446ubtgpio.cloudworkstations.dev/_next/static/chunks/%5Broot%20of%20the%20server%5D__b07dbc43._.css

@charset "utf-8";

@font-face { font-family: Geist; font-style: normal; font-weight: 100 900; =
font-display: swap; src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310=
ad.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4=
B0-4B1, U+2116; }

@font-face { font-family: Geist; font-style: normal; font-weight: 100 900; =
font-display: swap; src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a=
5b.woff2") format("woff2"); unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC,=
 U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF=
2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;=
 }

@font-face { font-family: Geist; font-style: normal; font-weight: 100 900; =
font-display: swap; src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef=
7.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2=
BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, =
U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }

@font-face { font-family: "Geist Fallback"; src: local("Arial"); ascent-ove=
rride: 95.94%; descent-override: 28.16%; line-gap-override: 0%; size-adjust=
: 104.76%; }

.geist_e531dabc-module__QGiZLq__className { font-family: Geist, "Geist Fall=
back"; font-style: normal; }

.geist_e531dabc-module__QGiZLq__variable { --font-geist-sans: "Geist", "Gei=
st Fallback"; }

@font-face { font-family: "Geist Mono"; font-style: normal; font-weight: 10=
0 900; font-display: swap; src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkj=
kotbA-s.cb6bbcb1.woff2") format("woff2"); unicode-range: U+301, U+400-45F, =
U+490-491, U+4B0-4B1, U+2116; }

@font-face { font-family: "Geist Mono"; font-style: normal; font-weight: 10=
0 900; font-display: swap; src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkj=
kotbA-s.e32db976.woff2") format("woff2"); unicode-range: U+100-2BA, U+2BD-2=
C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E=
00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F=
, U+A720-A7FF; }

@font-face { font-family: "Geist Mono"; font-style: normal; font-weight: 10=
0 900; font-display: swap; src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkj=
ko-s.p.be19f591.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+15=
2-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+=
20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }

@font-face { font-family: "Geist Mono Fallback"; src: local("Arial"); ascen=
t-override: 74.67%; descent-override: 21.92%; line-gap-override: 0%; size-a=
djust: 134.59%; }

.geist_mono_68a01160-module__YLcDdW__className { font-family: "Geist Mono",=
 "Geist Mono Fallback"; font-style: normal; }

.geist_mono_68a01160-module__YLcDdW__variable { --font-geist-mono: "Geist M=
ono", "Geist Mono Fallback"; }

*, ::before, ::after { --tw-border-spacing-x: 0; --tw-border-spacing-y: 0; =
--tw-translate-x: 0; --tw-translate-y: 0; --tw-rotate: 0; --tw-skew-x: 0; -=
-tw-skew-y: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: =
; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-=
from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ;=
 --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-s=
pacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-widt=
h: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: #3b82f680; --tw-ring=
-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0=
000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-c=
ontrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturat=
e: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-=
brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-bac=
kdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-b=
ackdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contai=
n-layout: ; --tw-contain-paint: ; --tw-contain-style: ; }

::backdrop { --tw-border-spacing-x: 0; --tw-border-spacing-y: 0; --tw-trans=
late-x: 0; --tw-translate-y: 0; --tw-rotate: 0; --tw-skew-x: 0; --tw-skew-y=
: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pin=
ch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-posit=
ion: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordi=
nal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; =
--tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --=
tw-ring-offset-color: #fff; --tw-ring-color: #3b82f680; --tw-ring-offset-sh=
adow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-=
shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ;=
 --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-=
sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness=
: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-=
rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-sa=
turate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: =
; --tw-contain-paint: ; --tw-contain-style: ; }

*, ::before, ::after { box-sizing: border-box; border-width: 0px; border-st=
yle: solid; border-color: rgb(229, 231, 235); }

::before, ::after { --tw-content: ""; }

html, :host { line-height: 1.5; text-size-adjust: 100%; tab-size: 4; font-f=
amily: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI=
 Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-feature-settings: norm=
al; font-variation-settings: normal; -webkit-tap-highlight-color: transpare=
nt; }

body { margin: 0px; line-height: inherit; }

hr { height: 0px; color: inherit; border-top-width: 1px; }

abbr:where([title]) { text-decoration: underline dotted; }

h1, h2, h3, h4, h5, h6 { font-size: inherit; font-weight: inherit; }

a { color: inherit; text-decoration: inherit; }

b, strong { font-weight: bolder; }

code, kbd, samp, pre { font-family: ui-monospace, SFMono-Regular, Menlo, Mo=
naco, Consolas, "Liberation Mono", "Courier New", monospace; font-feature-s=
ettings: normal; font-variation-settings: normal; font-size: 1em; }

small { font-size: 80%; }

sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-ali=
gn: baseline; }

sub { bottom: -0.25em; }

sup { top: -0.5em; }

table { text-indent: 0px; border-color: inherit; border-collapse: collapse;=
 }

button, input, optgroup, select, textarea { font-family: inherit; font-feat=
ure-settings: inherit; font-variation-settings: inherit; font-size: 100%; f=
ont-weight: inherit; line-height: inherit; letter-spacing: inherit; color: =
inherit; margin: 0px; padding: 0px; }

button, select { text-transform: none; }

button, input:where([type=3D"button"]), input:where([type=3D"reset"]), inpu=
t:where([type=3D"submit"]) { appearance: button; background-color: rgba(0, =
0, 0, 0); background-image: none; }

progress { vertical-align: baseline; }

::-webkit-inner-spin-button, ::-webkit-outer-spin-button { height: auto; }

[type=3D"search"] { appearance: textfield; outline-offset: -2px; }

::-webkit-search-decoration { appearance: none; }

::-webkit-file-upload-button { appearance: button; font: inherit; }

summary { display: list-item; }

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre { margin: 0p=
x; }

fieldset { margin: 0px; padding: 0px; }

legend { padding: 0px; }

ol, ul, menu { list-style: none; margin: 0px; padding: 0px; }

dialog { padding: 0px; }

textarea { resize: vertical; }

input::placeholder, textarea::placeholder { opacity: 1; color: rgb(156, 163=
, 175); }

button, [role=3D"button"] { cursor: pointer; }

:disabled { cursor: default; }

img, svg, video, canvas, audio, iframe, embed, object { display: block; ver=
tical-align: middle; }

img, video { max-width: 100%; height: auto; }

[hidden]:where(:not([hidden=3D"until-found"])) { display: none; }

:root { --background: 53 100% 98%; --foreground: 224 71% 10%; --card: 0 0% =
100%; --card-foreground: 224 71% 10%; --popover: 0 0% 100%; --popover-foreg=
round: 224 71% 10%; --primary: 356 85% 58%; --primary-foreground: 0 0% 100%=
; --secondary: 220 15% 92%; --secondary-foreground: 224 71% 10%; --muted: 2=
20 15% 92%; --muted-foreground: 215 20% 55%; --accent: 180 100% 25%; --acce=
nt-foreground: 0 0% 100%; --destructive: 0 84.2% 60.2%; --destructive-foreg=
round: 0 0% 98%; --border: 220 20% 88%; --input: 220 20% 88%; --ring: 180 1=
00% 30%; --radius: .5rem; --chart-1: 12 76% 61%; --chart-2: 173 58% 39%; --=
chart-3: 197 37% 24%; --chart-4: 43 74% 66%; --chart-5: 27 87% 67%; --sideb=
ar-background: 0 0% 98%; --sidebar-foreground: 240 5.3% 26.1%; --sidebar-pr=
imary: 240 5.9% 10%; --sidebar-primary-foreground: 0 0% 98%; --sidebar-acce=
nt: 240 4.8% 95.9%; --sidebar-accent-foreground: 240 5.9% 10%; --sidebar-bo=
rder: 220 13% 91%; --sidebar-ring: 217.2 91.2% 59.8%; }

.dark { --background: 222 47% 11%; --foreground: 210 40% 98%; --card: 222 4=
7% 11%; --card-foreground: 210 40% 98%; --popover: 222 47% 11%; --popover-f=
oreground: 210 40% 98%; --primary: 356 85% 58%; --primary-foreground: 0 0% =
100%; --secondary: 217 33% 17%; --secondary-foreground: 210 40% 98%; --mute=
d: 217 33% 17%; --muted-foreground: 215 20% 65%; --accent: 180 100% 30%; --=
accent-foreground: 0 0% 100%; --destructive: 0 63% 31%; --destructive-foreg=
round: 210 40% 98%; --border: 217 33% 22%; --input: 217 33% 22%; --ring: 18=
0 100% 35%; --chart-1: 220 70% 50%; --chart-2: 160 60% 45%; --chart-3: 30 8=
0% 55%; --chart-4: 280 65% 60%; --chart-5: 340 75% 55%; --sidebar-backgroun=
d: 240 5.9% 10%; --sidebar-foreground: 240 4.8% 95.9%; --sidebar-primary: 2=
24.3 76.3% 48%; --sidebar-primary-foreground: 0 0% 100%; --sidebar-accent: =
240 3.7% 15.9%; --sidebar-accent-foreground: 240 4.8% 95.9%; --sidebar-bord=
er: 240 3.7% 15.9%; --sidebar-ring: 217.2 91.2% 59.8%; }

* { border-color: hsl(var(--border)); }

body { background-color: hsl(var(--background)); color: hsl(var(--foregroun=
d)); }

.container { width: 100%; }

@media (width >=3D 640px) {
  .container { max-width: 640px; }
}

@media (width >=3D 768px) {
  .container { max-width: 768px; }
}

@media (width >=3D 1024px) {
  .container { max-width: 1024px; }
}

@media (width >=3D 1280px) {
  .container { max-width: 1280px; }
}

@media (width >=3D 1536px) {
  .container { max-width: 1536px; }
}

.sr-only { position: absolute; width: 1px; height: 1px; padding: 0px; margi=
n: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: now=
rap; border-width: 0px; }

.pointer-events-none { pointer-events: none; }

.pointer-events-auto { pointer-events: auto; }

.visible { visibility: visible; }

.invisible { visibility: hidden; }

.fixed { position: fixed; }

.absolute { position: absolute; }

.relative { position: relative; }

.inset-0 { inset: 0px; }

.inset-x-0 { left: 0px; right: 0px; }

.inset-y-0 { top: 0px; bottom: 0px; }

.bottom-0 { bottom: 0px; }

.left-0 { left: 0px; }

.left-1 { left: 0.25rem; }

.left-2 { left: 0.5rem; }

.left-\[50\%\] { left: 50%; }

.right-0 { right: 0px; }

.right-1 { right: 0.25rem; }

.right-2 { right: 0.5rem; }

.right-3 { right: 0.75rem; }

.right-4 { right: 1rem; }

.top-0 { top: 0px; }

.top-1\.5 { top: 0.375rem; }

.top-2 { top: 0.5rem; }

.top-3\.5 { top: 0.875rem; }

.top-4 { top: 1rem; }

.top-\[50\%\] { top: 50%; }

.z-10 { z-index: 10; }

.z-20 { z-index: 20; }

.z-50 { z-index: 50; }

.z-\[100\] { z-index: 100; }

.-mx-1 { margin-left: -0.25rem; margin-right: -0.25rem; }

.mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }

.mx-3\.5 { margin-left: 0.875rem; margin-right: 0.875rem; }

.mx-auto { margin-left: auto; margin-right: auto; }

.my-0\.5 { margin-top: 0.125rem; margin-bottom: 0.125rem; }

.my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }

.my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }

.mb-1 { margin-bottom: 0.25rem; }

.mb-10 { margin-bottom: 2.5rem; }

.mb-12 { margin-bottom: 3rem; }

.mb-2 { margin-bottom: 0.5rem; }

.mb-3 { margin-bottom: 0.75rem; }

.mb-4 { margin-bottom: 1rem; }

.mb-6 { margin-bottom: 1.5rem; }

.ml-2 { margin-left: 0.5rem; }

.ml-auto { margin-left: auto; }

.mr-2 { margin-right: 0.5rem; }

.mt-0\.5 { margin-top: 0.125rem; }

.mt-1\.5 { margin-top: 0.375rem; }

.mt-12 { margin-top: 3rem; }

.mt-2 { margin-top: 0.5rem; }

.mt-4 { margin-top: 1rem; }

.mt-6 { margin-top: 1.5rem; }

.block { display: block; }

.flex { display: flex; }

.inline-flex { display: inline-flex; }

.table { display: table; }

.grid { display: grid; }

.hidden { display: none; }

.aspect-square { aspect-ratio: 1 / 1; }

.aspect-video { aspect-ratio: 16 / 9; }

.size-4 { width: 1rem; height: 1rem; }

.h-10 { height: 2.5rem; }

.h-11 { height: 2.75rem; }

.h-12 { height: 3rem; }

.h-2 { height: 0.5rem; }

.h-2\.5 { height: 0.625rem; }

.h-3\.5 { height: 0.875rem; }

.h-4 { height: 1rem; }

.h-5 { height: 1.25rem; }

.h-6 { height: 1.5rem; }

.h-7 { height: 1.75rem; }

.h-8 { height: 2rem; }

.h-9 { height: 2.25rem; }

.h-\[1px\] { height: 1px; }

.h-\[var\(--radix-select-trigger-height\)\] { height: var(--radix-select-tr=
igger-height); }

.h-full { height: 100%; }

.h-px { height: 1px; }

.h-svh { height: 100svh; }

.max-h-96 { max-height: 24rem; }

.max-h-screen { max-height: 100vh; }

.min-h-0 { min-height: 0px; }

.min-h-\[80px\] { min-height: 80px; }

.min-h-screen { min-height: 100vh; }

.min-h-svh { min-height: 100svh; }

.w-0 { width: 0px; }

.w-1 { width: 0.25rem; }

.w-10 { width: 2.5rem; }

.w-11 { width: 2.75rem; }

.w-12 { width: 3rem; }

.w-2 { width: 0.5rem; }

.w-2\.5 { width: 0.625rem; }

.w-3\.5 { width: 0.875rem; }

.w-3\/4 { width: 75%; }

.w-4 { width: 1rem; }

.w-5 { width: 1.25rem; }

.w-6 { width: 1.5rem; }

.w-7 { width: 1.75rem; }

.w-72 { width: 18rem; }

.w-8 { width: 2rem; }

.w-9 { width: 2.25rem; }

.w-\[--sidebar-width\] { width: var(--sidebar-width); }

.w-\[1px\] { width: 1px; }

.w-auto { width: auto; }

.w-full { width: 100%; }

.min-w-0 { min-width: 0px; }

.min-w-5 { min-width: 1.25rem; }

.min-w-\[12rem\] { min-width: 12rem; }

.min-w-\[8rem\] { min-width: 8rem; }

.min-w-\[var\(--radix-select-trigger-width\)\] { min-width: var(--radix-sel=
ect-trigger-width); }

.max-w-3xl { max-width: 48rem; }

.max-w-\[--skeleton-width\] { max-width: var(--skeleton-width); }

.max-w-lg { max-width: 32rem; }

.max-w-none { max-width: none; }

.flex-1 { flex: 1 1 0%; }

.flex-shrink-0 { flex-shrink: 0; }

.shrink-0 { flex-shrink: 0; }

.flex-grow { flex-grow: 1; }

.grow { flex-grow: 1; }

.caption-bottom { caption-side: bottom; }

.border-collapse { border-collapse: collapse; }

.-translate-x-1\/2 { --tw-translate-x: -50%; transform: translate(var(--tw-=
translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--t=
w-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--t=
w-scale-y)); }

.-translate-x-px { --tw-translate-x: -1px; transform: translate(var(--tw-tr=
anslate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-=
skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-=
scale-y)); }

.translate-x-\[-50\%\] { --tw-translate-x: -50%; transform: translate(var(-=
-tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var=
(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var=
(--tw-scale-y)); }

.translate-x-px { --tw-translate-x: 1px; transform: translate(var(--tw-tran=
slate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-sk=
ew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-sc=
ale-y)); }

.translate-y-\[-50\%\] { --tw-translate-y: -50%; transform: translate(var(-=
-tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var=
(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var=
(--tw-scale-y)); }

.transform { transform: translate(var(--tw-translate-x), var(--tw-translate=
-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y=
)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

@keyframes pulse {=20
  50% { opacity: 0.5; }
}

.animate-pulse { animation: 2s cubic-bezier(0.4, 0, 0.6, 1) 0s infinite nor=
mal none running pulse; }

@keyframes spin {=20
  100% { transform: rotate(360deg); }
}

.animate-spin { animation: 1s linear 0s infinite normal none running spin; =
}

.cursor-default { cursor: default; }

.cursor-pointer { cursor: pointer; }

.touch-none { touch-action: none; }

.select-none { user-select: none; }

.list-inside { list-style-position: inside; }

.list-disc { list-style-type: disc; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0px, 1fr)); }

.flex-col { flex-direction: column; }

.flex-col-reverse { flex-direction: column-reverse; }

.flex-wrap { flex-wrap: wrap; }

.items-start { align-items: flex-start; }

.items-end { align-items: flex-end; }

.items-center { align-items: center; }

.items-stretch { align-items: stretch; }

.justify-center { justify-content: center; }

.justify-between { justify-content: space-between; }

.gap-1 { gap: 0.25rem; }

.gap-1\.5 { gap: 0.375rem; }

.gap-2 { gap: 0.5rem; }

.gap-4 { gap: 1rem; }

.gap-6 { gap: 1.5rem; }

.gap-8 { gap: 2rem; }

.space-x-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; mar=
gin-right: calc(.25rem * var(--tw-space-x-reverse)); margin-left: calc(.25r=
em * calc(1 - var(--tw-space-x-reverse))); }

.space-x-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; mar=
gin-right: calc(.5rem * var(--tw-space-x-reverse)); margin-left: calc(.5rem=
 * calc(1 - var(--tw-space-x-reverse))); }

.space-x-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; mar=
gin-right: calc(1rem * var(--tw-space-x-reverse)); margin-left: calc(1rem *=
 calc(1 - var(--tw-space-x-reverse))); }

.space-y-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom:=
 calc(.25rem * var(--tw-space-y-reverse)); }

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; =
margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse))); margin-bot=
tom: calc(.375rem * var(--tw-space-y-reverse)); }

.space-y-10 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; ma=
rgin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom=
: calc(2.5rem * var(--tw-space-y-reverse)); }

.space-y-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: =
calc(.5rem * var(--tw-space-y-reverse)); }

.space-y-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: c=
alc(1rem * var(--tw-space-y-reverse)); }

.space-y-6 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom:=
 calc(1.5rem * var(--tw-space-y-reverse)); }

.space-y-8 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: c=
alc(2rem * var(--tw-space-y-reverse)); }

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; =
margin-top: calc(.125rem * calc(1 - var(--tw-space-y-reverse))); margin-bot=
tom: calc(.125rem * var(--tw-space-y-reverse)); }

.overflow-auto { overflow: auto; }

.overflow-hidden { overflow: hidden; }

.whitespace-nowrap { white-space: nowrap; }

.rounded-\[2px\] { border-radius: 2px; }

.rounded-\[inherit\] { border-radius: inherit; }

.rounded-full { border-radius: 9999px; }

.rounded-lg { border-radius: var(--radius); }

.rounded-md { border-radius: calc(var(--radius)  - 2px); }

.rounded-sm { border-radius: calc(var(--radius)  - 4px); }

.rounded-t-md { border-top-left-radius: calc(var(--radius)  - 2px); border-=
top-right-radius: calc(var(--radius)  - 2px); }

.border { border-width: 1px; }

.border-2 { border-width: 2px; }

.border-\[1\.5px\] { border-width: 1.5px; }

.border-b { border-bottom-width: 1px; }

.border-l { border-left-width: 1px; }

.border-r { border-right-width: 1px; }

.border-t { border-top-width: 1px; }

.border-t-2 { border-top-width: 2px; }

.border-dashed { border-style: dashed; }

.border-\[--color-border\] { border-color: var(--color-border); }

.border-accent { border-color: hsl(var(--accent)); }

.border-border { border-color: hsl(var(--border)); }

.border-border\/50 { border-color: hsl(var(--border) / .5); }

.border-destructive { border-color: hsl(var(--destructive)); }

.border-destructive\/50 { border-color: hsl(var(--destructive) / .5); }

.border-input { border-color: hsl(var(--input)); }

.border-primary { border-color: hsl(var(--primary)); }

.border-sidebar-border { border-color: hsl(var(--sidebar-border)); }

.border-transparent { border-color: rgba(0, 0, 0, 0); }

.border-l-transparent { border-left-color: rgba(0, 0, 0, 0); }

.border-t-transparent { border-top-color: rgba(0, 0, 0, 0); }

.bg-\[--color-bg\] { background-color: var(--color-bg); }

.bg-accent { background-color: hsl(var(--accent)); }

.bg-background { background-color: hsl(var(--background)); }

.bg-black\/80 { background-color: rgba(0, 0, 0, 0.8); }

.bg-border { background-color: hsl(var(--border)); }

.bg-card { background-color: hsl(var(--card)); }

.bg-destructive { background-color: hsl(var(--destructive)); }

.bg-green-500 { --tw-bg-opacity: 1; background-color: rgb(34 197 94 / var(-=
-tw-bg-opacity, 1)); }

.bg-muted { background-color: hsl(var(--muted)); }

.bg-muted\/50 { background-color: hsl(var(--muted) / .5); }

.bg-popover { background-color: hsl(var(--popover)); }

.bg-primary { background-color: hsl(var(--primary)); }

.bg-secondary { background-color: hsl(var(--secondary)); }

.bg-sidebar { background-color: hsl(var(--sidebar-background)); }

.bg-sidebar-border { background-color: hsl(var(--sidebar-border)); }

.bg-transparent { background-color: rgba(0, 0, 0, 0); }

.bg-blue-500 { --tw-bg-opacity: 1; background-color: rgb(59 130 246 / var(-=
-tw-bg-opacity, 1)); }

.fill-current { fill: currentcolor; }

.p-0 { padding: 0px; }

.p-1 { padding: 0.25rem; }

.p-2 { padding: 0.5rem; }

.p-3 { padding: 0.75rem; }

.p-4 { padding: 1rem; }

.p-6 { padding: 1.5rem; }

.p-\[1px\] { padding: 1px; }

.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }

.px-2\.5 { padding-left: 0.625rem; padding-right: 0.625rem; }

.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }

.px-4 { padding-left: 1rem; padding-right: 1rem; }

.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }

.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }

.py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }

.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }

.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.pb-3 { padding-bottom: 0.75rem; }

.pb-4 { padding-bottom: 1rem; }

.pl-8 { padding-left: 2rem; }

.pr-2 { padding-right: 0.5rem; }

.pr-8 { padding-right: 2rem; }

.pt-0 { padding-top: 0px; }

.pt-1 { padding-top: 0.25rem; }

.pt-2 { padding-top: 0.5rem; }

.pt-3 { padding-top: 0.75rem; }

.pl-2 { padding-left: 0.5rem; }

.text-left { text-align: left; }

.text-center { text-align: center; }

.text-right { text-align: right; }

.align-middle { vertical-align: middle; }

.font-mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Cons=
olas, "Liberation Mono", "Courier New", monospace; }

.text-2xl { font-size: 1.5rem; line-height: 2rem; }

.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }

.text-\[0\.8rem\] { font-size: 0.8rem; }

.text-base { font-size: 1rem; line-height: 1.5rem; }

.text-lg { font-size: 1.125rem; line-height: 1.75rem; }

.text-sm { font-size: 0.875rem; line-height: 1.25rem; }

.text-xl { font-size: 1.25rem; line-height: 1.75rem; }

.text-xs { font-size: 0.75rem; line-height: 1rem; }

.font-bold { font-weight: 700; }

.font-extrabold { font-weight: 800; }

.font-medium { font-weight: 500; }

.font-normal { font-weight: 400; }

.font-semibold { font-weight: 600; }

.tabular-nums { --tw-numeric-spacing: tabular-nums; font-variant-numeric: v=
ar(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-n=
umeric-spacing) var(--tw-numeric-fraction); }

.leading-none { line-height: 1; }

.tracking-tight { letter-spacing: -0.025em; }

.tracking-widest { letter-spacing: 0.1em; }

.text-accent { color: hsl(var(--accent)); }

.text-accent-foreground { color: hsl(var(--accent-foreground)); }

.text-card-foreground { color: hsl(var(--card-foreground)); }

.text-current { color: currentcolor; }

.text-destructive { color: hsl(var(--destructive)); }

.text-destructive-foreground { color: hsl(var(--destructive-foreground)); }

.text-foreground { color: hsl(var(--foreground)); }

.text-foreground\/50 { color: hsl(var(--foreground) / .5); }

.text-green-500 { --tw-text-opacity: 1; color: rgb(34 197 94 / var(--tw-tex=
t-opacity, 1)); }

.text-muted-foreground { color: hsl(var(--muted-foreground)); }

.text-popover-foreground { color: hsl(var(--popover-foreground)); }

.text-primary { color: hsl(var(--primary)); }

.text-primary-foreground { color: hsl(var(--primary-foreground)); }

.text-secondary-foreground { color: hsl(var(--secondary-foreground)); }

.text-sidebar-foreground { color: hsl(var(--sidebar-foreground)); }

.text-sidebar-foreground\/70 { color: hsl(var(--sidebar-foreground) / .7); =
}

.text-white { --tw-text-opacity: 1; color: rgb(255 255 255 / var(--tw-text-=
opacity, 1)); }

.underline-offset-4 { text-underline-offset: 4px; }

.antialiased { -webkit-font-smoothing: antialiased; }

.opacity-0 { opacity: 0; }

.opacity-50 { opacity: 0.5; }

.opacity-60 { opacity: 0.6; }

.opacity-70 { opacity: 0.7; }

.opacity-90 { opacity: 0.9; }

.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] { --tw-shadow: 0 0 0 1=
px hsl(var(--sidebar-border)); --tw-shadow-colored: 0 0 0 1px var(--tw-shad=
ow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ri=
ng-shadow, 0 0 #0000), var(--tw-shadow); }

.shadow-lg { --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #00000=
01a; --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6p=
x -4px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0=
 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }

.shadow-md { --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001=
a; --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2=
px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #00=
00), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }

.shadow-none { --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; box-=
shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 =
#0000), var(--tw-shadow); }

.shadow-sm { --tw-shadow: 0 1px 2px 0 #0000000d; --tw-shadow-colored: 0 1px=
 2px 0 var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0=
 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }

.shadow-xl { --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000=
001a; --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 1=
0px -6px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0=
 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }

.outline-none { outline: rgba(0, 0, 0, 0) solid 2px; outline-offset: 2px; }

.outline { outline-style: solid; }

.ring-0 { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring=
-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring=
-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);=
 box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-=
shadow, 0 0 #0000); }

.ring-2 { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring=
-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring=
-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);=
 box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-=
shadow, 0 0 #0000); }

.ring-4 { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring=
-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring=
-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);=
 box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-=
shadow, 0 0 #0000); }

.ring-accent { --tw-ring-color: hsl(var(--accent)); }

.ring-primary { --tw-ring-color: hsl(var(--primary)); }

.ring-primary\/30 { --tw-ring-color: hsl(var(--primary) / .3); }

.ring-sidebar-ring { --tw-ring-color: hsl(var(--sidebar-ring)); }

.ring-offset-background { --tw-ring-offset-color: hsl(var(--background)); }

.filter { filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) va=
r(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) =
var(--tw-sepia) var(--tw-drop-shadow); }

.transition { transition-property: color, background-color, border-color, t=
ext-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,=
 backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);=
 transition-duration: 0.15s; }

.transition-\[left\,right\,width\] { transition-property: left, right, widt=
h; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-dur=
ation: 0.15s; }

.transition-\[margin\,opa\] { transition-property: margin, opa; transition-=
timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; =
}

.transition-\[width\,height\,padding\] { transition-property: width, height=
, padding; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transi=
tion-duration: 0.15s; }

.transition-\[width\] { transition-property: width; transition-timing-funct=
ion: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-all { transition-property: all; transition-timing-function: cub=
ic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-colors { transition-property: color, background-color, border-c=
olor, text-decoration-color, fill, stroke; transition-timing-function: cubi=
c-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-opacity { transition-property: opacity; transition-timing-funct=
ion: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-transform { transition-property: transform; transition-timing-f=
unction: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.duration-200 { transition-duration: 0.2s; }

.duration-300 { transition-duration: 0.3s; }

.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

.ease-linear { transition-timing-function: linear; }

@keyframes enter {=20
  0% { opacity: var(--tw-enter-opacity, 1); transform: translate3d(var(--tw=
-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw=
-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotat=
e(var(--tw-enter-rotate, 0)); }
}

@keyframes exit {=20
  100% { opacity: var(--tw-exit-opacity, 1); transform: translate3d(var(--t=
w-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-=
exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(va=
r(--tw-exit-rotate, 0)); }
}

.animate-in { animation-name: enter; animation-duration: 0.15s; --tw-enter-=
opacity: initial; --tw-enter-scale: initial; --tw-enter-rotate: initial; --=
tw-enter-translate-x: initial; --tw-enter-translate-y: initial; }

.fade-in-0 { --tw-enter-opacity: 0; }

.zoom-in-95 { --tw-enter-scale: .95; }

.duration-200 { animation-duration: 0.2s; }

.duration-300 { animation-duration: 0.3s; }

.ease-in-out { animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

.ease-linear { animation-timing-function: linear; }

.file\:border-0::file-selector-button { border-width: 0px; }

.file\:bg-transparent::file-selector-button { background-color: rgba(0, 0, =
0, 0); }

.file\:text-sm::file-selector-button { font-size: 0.875rem; line-height: 1.=
25rem; }

.file\:font-medium::file-selector-button { font-weight: 500; }

.file\:text-foreground::file-selector-button { color: hsl(var(--foreground)=
); }

.placeholder\:text-muted-foreground::placeholder { color: hsl(var(--muted-f=
oreground)); }

.after\:absolute::after { content: var(--tw-content); position: absolute; }

.after\:-inset-2::after { content: var(--tw-content); inset: -0.5rem; }

.after\:inset-y-0::after { content: var(--tw-content); top: 0px; bottom: 0p=
x; }

.after\:left-1\/2::after { content: var(--tw-content); left: 50%; }

.after\:w-\[2px\]::after { content: var(--tw-content); width: 2px; }

.focus-within\:relative:focus-within { position: relative; }

.focus-within\:z-20:focus-within { z-index: 20; }

.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform=
: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-r=
otate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-sca=
le-x)) scaleY(var(--tw-scale-y)); }

.hover\:bg-accent:hover { background-color: hsl(var(--accent)); }

.hover\:bg-destructive\/80:hover { background-color: hsl(var(--destructive)=
 / .8); }

.hover\:bg-destructive\/90:hover { background-color: hsl(var(--destructive)=
 / .9); }

.hover\:bg-muted\/50:hover { background-color: hsl(var(--muted) / .5); }

.hover\:bg-primary:hover { background-color: hsl(var(--primary)); }

.hover\:bg-primary\/80:hover { background-color: hsl(var(--primary) / .8); =
}

.hover\:bg-primary\/90:hover { background-color: hsl(var(--primary) / .9); =
}

.hover\:bg-secondary:hover { background-color: hsl(var(--secondary)); }

.hover\:bg-secondary\/80:hover { background-color: hsl(var(--secondary) / .=
8); }

.hover\:bg-sidebar-accent:hover { background-color: hsl(var(--sidebar-accen=
t)); }

.hover\:text-accent-foreground:hover { color: hsl(var(--accent-foreground))=
; }

.hover\:text-foreground:hover { color: hsl(var(--foreground)); }

.hover\:text-primary-foreground:hover { color: hsl(var(--primary-foreground=
)); }

.hover\:text-sidebar-accent-foreground:hover { color: hsl(var(--sidebar-acc=
ent-foreground)); }

.hover\:underline:hover { text-decoration-line: underline; }

.hover\:no-underline:hover { text-decoration-line: none; }

.hover\:opacity-100:hover { opacity: 1; }

.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover { --tw-sh=
adow: 0 0 0 1px hsl(var(--sidebar-accent)); --tw-shadow-colored: 0 0 0 1px =
var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000)=
, var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }

.hover\:shadow-xl:hover { --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10=
px -6px #0000001a; --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-co=
lor), 0 8px 10px -6px var(--tw-shadow-color); box-shadow: var(--tw-ring-off=
set-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);=
 }

.hover\:after\:bg-sidebar-border:hover::after { content: var(--tw-content);=
 background-color: hsl(var(--sidebar-border)); }

.focus\:bg-accent:focus { background-color: hsl(var(--accent)); }

.focus\:bg-primary:focus { background-color: hsl(var(--primary)); }

.focus\:text-accent-foreground:focus { color: hsl(var(--accent-foreground))=
; }

.focus\:text-primary-foreground:focus { color: hsl(var(--primary-foreground=
)); }

.focus\:opacity-100:focus { opacity: 1; }

.focus\:outline-none:focus { outline: rgba(0, 0, 0, 0) solid 2px; outline-o=
ffset: 2px; }

.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 =
var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: =
var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw=
-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shado=
w), var(--tw-shadow, 0 0 #0000); }

.focus\:ring-ring:focus { --tw-ring-color: hsl(var(--ring)); }

.focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }

.focus-visible\:outline-none:focus-visible { outline: rgba(0, 0, 0, 0) soli=
d 2px; outline-offset: 2px; }

.focus-visible\:ring-2:focus-visible { --tw-ring-offset-shadow: var(--tw-ri=
ng-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --=
tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-=
width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var=
(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }

.focus-visible\:ring-ring:focus-visible { --tw-ring-color: hsl(var(--ring))=
; }

.focus-visible\:ring-sidebar-ring:focus-visible { --tw-ring-color: hsl(var(=
--sidebar-ring)); }

.focus-visible\:ring-offset-2:focus-visible { --tw-ring-offset-width: 2px; =
}

.focus-visible\:ring-offset-background:focus-visible { --tw-ring-offset-col=
or: hsl(var(--background)); }

.active\:bg-sidebar-accent:active { background-color: hsl(var(--sidebar-acc=
ent)); }

.active\:text-sidebar-accent-foreground:active { color: hsl(var(--sidebar-a=
ccent-foreground)); }

.disabled\:pointer-events-none:disabled { pointer-events: none; }

.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

.disabled\:opacity-50:disabled { opacity: 0.5; }

.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 =
{ opacity: 1; }

.group:hover .group-hover\:border-foreground { border-color: hsl(var(--fore=
ground)); }

.group\/menu-item:hover .group-hover\/menu-item\:opacity-100 { opacity: 1; =
}

.group:hover .group-hover\:opacity-100 { opacity: 1; }

.group.destructive .group-\[\.destructive\]\:border-muted\/40 { border-colo=
r: hsl(var(--muted) / .4); }

.group.destructive .group-\[\.destructive\]\:text-red-300 { --tw-text-opaci=
ty: 1; color: rgb(252 165 165 / var(--tw-text-opacity, 1)); }

.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:=
hover { border-color: hsl(var(--destructive) / .3); }

.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover { =
background-color: hsl(var(--destructive)); }

.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foregr=
ound:hover { color: hsl(var(--destructive-foreground)); }

.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover { --t=
w-text-opacity: 1; color: rgb(254 242 242 / var(--tw-text-opacity, 1)); }

.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus =
{ --tw-ring-color: hsl(var(--destructive)); }

.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus { --=
tw-ring-opacity: 1; --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacit=
y, 1)); }

.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:foc=
us { --tw-ring-offset-color: #dc2626; }

.peer\/menu-button:hover ~ .peer-hover\/menu-button\:text-sidebar-accent-fo=
reground { color: hsl(var(--sidebar-accent-foreground)); }

.peer:disabled ~ .peer-disabled\:cursor-not-allowed { cursor: not-allowed; =
}

.peer:disabled ~ .peer-disabled\:opacity-70 { opacity: 0.7; }

.has-\[\[data-variant\=3Dinset\]\]\:bg-sidebar:has([data-variant=3D"inset"]=
) { background-color: hsl(var(--sidebar-background)); }

.group\/menu-item:has([data-sidebar=3D"menu-action"]) .group-has-\[\[data-s=
idebar\=3Dmenu-action\]\]\/menu-item\:pr-8 { padding-right: 2rem; }

.aria-disabled\:pointer-events-none[aria-disabled=3D"true"] { pointer-event=
s: none; }

.aria-disabled\:opacity-50[aria-disabled=3D"true"] { opacity: 0.5; }

.aria-selected\:bg-accent[aria-selected=3D"true"] { background-color: hsl(v=
ar(--accent)); }

.aria-selected\:bg-accent\/50[aria-selected=3D"true"] { background-color: h=
sl(var(--accent) / .5); }

.aria-selected\:text-accent-foreground[aria-selected=3D"true"] { color: hsl=
(var(--accent-foreground)); }

.aria-selected\:text-muted-foreground[aria-selected=3D"true"] { color: hsl(=
var(--muted-foreground)); }

.aria-selected\:opacity-100[aria-selected=3D"true"] { opacity: 1; }

.data-\[disabled\]\:pointer-events-none[data-disabled] { pointer-events: no=
ne; }

.data-\[side\=3Dbottom\]\:translate-y-1[data-side=3D"bottom"] { --tw-transl=
ate-y: .25rem; transform: translate(var(--tw-translate-x), var(--tw-transla=
te-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew=
-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[side\=3Dleft\]\:-translate-x-1[data-side=3D"left"] { --tw-translate=
-x: -.25rem; transform: translate(var(--tw-translate-x), var(--tw-translate=
-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y=
)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[side\=3Dright\]\:translate-x-1[data-side=3D"right"] { --tw-translat=
e-x: .25rem; transform: translate(var(--tw-translate-x), var(--tw-translate=
-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y=
)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[side\=3Dtop\]\:-translate-y-1[data-side=3D"top"] { --tw-translate-y=
: -.25rem; transform: translate(var(--tw-translate-x), var(--tw-translate-y=
)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))=
 scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[state\=3Dchecked\]\:translate-x-5[data-state=3D"checked"] { --tw-tr=
anslate-x: 1.25rem; transform: translate(var(--tw-translate-x), var(--tw-tr=
anslate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw=
-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[state\=3Dunchecked\]\:translate-x-0[data-state=3D"unchecked"] { --t=
w-translate-x: 0px; transform: translate(var(--tw-translate-x), var(--tw-tr=
anslate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw=
-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[swipe\=3Dcancel\]\:translate-x-0[data-swipe=3D"cancel"] { --tw-tran=
slate-x: 0px; transform: translate(var(--tw-translate-x), var(--tw-translat=
e-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-=
y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[swipe\=3Dend\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][da=
ta-swipe=3D"end"] { --tw-translate-x: var(--radix-toast-swipe-end-x); trans=
form: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--=
tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw=
-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[swipe\=3Dmove\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][=
data-swipe=3D"move"] { --tw-translate-x: var(--radix-toast-swipe-move-x); t=
ransform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(va=
r(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(=
--tw-scale-x)) scaleY(var(--tw-scale-y)); }

@keyframes accordion-up {=20
  0% { height: var(--radix-accordion-content-height); }
  100% { height: 0px; }
}

.data-\[state\=3Dclosed\]\:animate-accordion-up[data-state=3D"closed"] { an=
imation: 0.2s ease-out 0s 1 normal none running accordion-up; }

@keyframes accordion-down {=20
  0% { height: 0px; }
  100% { height: var(--radix-accordion-content-height); }
}

.data-\[state\=3Dopen\]\:animate-accordion-down[data-state=3D"open"] { anim=
ation: 0.2s ease-out 0s 1 normal none running accordion-down; }

.data-\[active\=3Dtrue\]\:bg-sidebar-accent[data-active=3D"true"] { backgro=
und-color: hsl(var(--sidebar-accent)); }

.data-\[state\=3Dactive\]\:bg-background[data-state=3D"active"] { backgroun=
d-color: hsl(var(--background)); }

.data-\[state\=3Dchecked\]\:bg-primary[data-state=3D"checked"] { background=
-color: hsl(var(--primary)); }

.data-\[state\=3Dopen\]\:bg-accent[data-state=3D"open"] { background-color:=
 hsl(var(--accent)); }

.data-\[state\=3Dopen\]\:bg-secondary[data-state=3D"open"] { background-col=
or: hsl(var(--secondary)); }

.data-\[state\=3Dselected\]\:bg-muted[data-state=3D"selected"] { background=
-color: hsl(var(--muted)); }

.data-\[state\=3Dunchecked\]\:bg-input[data-state=3D"unchecked"] { backgrou=
nd-color: hsl(var(--input)); }

.data-\[active\=3Dtrue\]\:font-medium[data-active=3D"true"] { font-weight: =
500; }

.data-\[active\=3Dtrue\]\:text-sidebar-accent-foreground[data-active=3D"tru=
e"] { color: hsl(var(--sidebar-accent-foreground)); }

.data-\[state\=3Dactive\]\:text-foreground[data-state=3D"active"] { color: =
hsl(var(--foreground)); }

.data-\[state\=3Dchecked\]\:text-primary-foreground[data-state=3D"checked"]=
 { color: hsl(var(--primary-foreground)); }

.data-\[state\=3Dopen\]\:text-accent-foreground[data-state=3D"open"] { colo=
r: hsl(var(--accent-foreground)); }

.data-\[state\=3Dopen\]\:text-muted-foreground[data-state=3D"open"] { color=
: hsl(var(--muted-foreground)); }

.data-\[disabled\]\:opacity-50[data-disabled] { opacity: 0.5; }

.data-\[state\=3Dopen\]\:opacity-100[data-state=3D"open"] { opacity: 1; }

.data-\[state\=3Dactive\]\:shadow-sm[data-state=3D"active"] { --tw-shadow: =
0 1px 2px 0 #0000000d; --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-col=
or); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-sha=
dow, 0 0 #0000), var(--tw-shadow); }

.data-\[swipe\=3Dmove\]\:transition-none[data-swipe=3D"move"] { transition-=
property: none; }

.data-\[state\=3Dclosed\]\:duration-300[data-state=3D"closed"] { transition=
-duration: 0.3s; }

.data-\[state\=3Dopen\]\:duration-500[data-state=3D"open"] { transition-dur=
ation: 0.5s; }

.data-\[state\=3Dopen\]\:animate-in[data-state=3D"open"] { animation-name: =
enter; animation-duration: 0.15s; --tw-enter-opacity: initial; --tw-enter-s=
cale: initial; --tw-enter-rotate: initial; --tw-enter-translate-x: initial;=
 --tw-enter-translate-y: initial; }

.data-\[state\=3Dclosed\]\:animate-out[data-state=3D"closed"] { animation-n=
ame: exit; animation-duration: 0.15s; --tw-exit-opacity: initial; --tw-exit=
-scale: initial; --tw-exit-rotate: initial; --tw-exit-translate-x: initial;=
 --tw-exit-translate-y: initial; }

.data-\[swipe\=3Dend\]\:animate-out[data-swipe=3D"end"] { animation-name: e=
xit; animation-duration: 0.15s; --tw-exit-opacity: initial; --tw-exit-scale=
: initial; --tw-exit-rotate: initial; --tw-exit-translate-x: initial; --tw-=
exit-translate-y: initial; }

.data-\[state\=3Dclosed\]\:fade-out-0[data-state=3D"closed"] { --tw-exit-op=
acity: 0; }

.data-\[state\=3Dclosed\]\:fade-out-80[data-state=3D"closed"] { --tw-exit-o=
pacity: .8; }

.data-\[state\=3Dopen\]\:fade-in-0[data-state=3D"open"] { --tw-enter-opacit=
y: 0; }

.data-\[state\=3Dclosed\]\:zoom-out-95[data-state=3D"closed"] { --tw-exit-s=
cale: .95; }

.data-\[state\=3Dopen\]\:zoom-in-95[data-state=3D"open"] { --tw-enter-scale=
: .95; }

.data-\[side\=3Dbottom\]\:slide-in-from-top-2[data-side=3D"bottom"] { --tw-=
enter-translate-y: -.5rem; }

.data-\[side\=3Dleft\]\:slide-in-from-right-2[data-side=3D"left"] { --tw-en=
ter-translate-x: .5rem; }

.data-\[side\=3Dright\]\:slide-in-from-left-2[data-side=3D"right"] { --tw-e=
nter-translate-x: -.5rem; }

.data-\[side\=3Dtop\]\:slide-in-from-bottom-2[data-side=3D"top"] { --tw-ent=
er-translate-y: .5rem; }

.data-\[state\=3Dclosed\]\:slide-out-to-bottom[data-state=3D"closed"] { --t=
w-exit-translate-y: 100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-left[data-state=3D"closed"] { --tw-=
exit-translate-x: -100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-left-1\/2[data-state=3D"closed"] { =
--tw-exit-translate-x: -50%; }

.data-\[state\=3Dclosed\]\:slide-out-to-right[data-state=3D"closed"] { --tw=
-exit-translate-x: 100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-right-full[data-state=3D"closed"] {=
 --tw-exit-translate-x: 100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-top[data-state=3D"closed"] { --tw-e=
xit-translate-y: -100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-top-\[48\%\][data-state=3D"closed"]=
 { --tw-exit-translate-y: -48%; }

.data-\[state\=3Dopen\]\:slide-in-from-bottom[data-state=3D"open"] { --tw-e=
nter-translate-y: 100%; }

.data-\[state\=3Dopen\]\:slide-in-from-left[data-state=3D"open"] { --tw-ent=
er-translate-x: -100%; }

.data-\[state\=3Dopen\]\:slide-in-from-left-1\/2[data-state=3D"open"] { --t=
w-enter-translate-x: -50%; }

.data-\[state\=3Dopen\]\:slide-in-from-right[data-state=3D"open"] { --tw-en=
ter-translate-x: 100%; }

.data-\[state\=3Dopen\]\:slide-in-from-top[data-state=3D"open"] { --tw-ente=
r-translate-y: -100%; }

.data-\[state\=3Dopen\]\:slide-in-from-top-\[48\%\][data-state=3D"open"] { =
--tw-enter-translate-y: -48%; }

.data-\[state\=3Dopen\]\:slide-in-from-top-full[data-state=3D"open"] { --tw=
-enter-translate-y: -100%; }

.data-\[state\=3Dclosed\]\:duration-300[data-state=3D"closed"] { animation-=
duration: 0.3s; }

.data-\[state\=3Dopen\]\:duration-500[data-state=3D"open"] { animation-dura=
tion: 0.5s; }

.data-\[state\=3Dopen\]\:hover\:bg-sidebar-accent:hover[data-state=3D"open"=
] { background-color: hsl(var(--sidebar-accent)); }

.data-\[state\=3Dopen\]\:hover\:text-sidebar-accent-foreground:hover[data-s=
tate=3D"open"] { color: hsl(var(--sidebar-accent-foreground)); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] { left: calc(var(--sideba=
r-width) * -1); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] { right: calc(var(--side=
bar-width) * -1); }

.group[data-side=3D"left"] .group-data-\[side\=3Dleft\]\:-right-4 { right: =
-1rem; }

.group[data-side=3D"right"] .group-data-\[side\=3Dright\]\:left-0 { left: 0=
px; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:-mt-=
8 { margin-top: -2rem; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:hidd=
en { display: none; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:\!si=
ze-8 { width: 2rem !important; height: 2rem !important; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:w-\[=
--sidebar-width-icon\] { width: var(--sidebar-width-icon); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:w-\[=
calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\] { width: calc(=
var(--sidebar-width-icon)  + 1rem); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:w-\[=
calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\] { width:=
 calc(var(--sidebar-width-icon)  + 1rem + 2px); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:w-0 { width: 0px; }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:translate-x-0 { --tw-translate-x: 0px; transform: translate(var(--tw-=
translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--t=
w-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--t=
w-scale-y)); }

.group[data-side=3D"right"] .group-data-\[side\=3Dright\]\:rotate-180 { --t=
w-rotate: 180deg; transform: translate(var(--tw-translate-x), var(--tw-tran=
slate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-s=
kew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:over=
flow-hidden { overflow: hidden; }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:roun=
ded-lg { border-radius: var(--radius); }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:bord=
er { border-width: 1px; }

.group[data-side=3D"left"] .group-data-\[side\=3Dleft\]\:border-r { border-=
right-width: 1px; }

.group[data-side=3D"right"] .group-data-\[side\=3Dright\]\:border-l { borde=
r-left-width: 1px; }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:bord=
er-sidebar-border { border-color: hsl(var(--sidebar-border)); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:\!p-=
0 { padding: 0px !important; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:\!p-=
2 { padding: 0.5rem !important; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:opac=
ity-0 { opacity: 0; }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:shad=
ow { --tw-shadow: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a; --tw-sha=
dow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-sh=
adow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-=
ring-shadow, 0 0 #0000), var(--tw-shadow); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:after\:left-full::after { content: var(--tw-content); left: 100%; }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:hover\:bg-sidebar:hover { background-color: hsl(var(--sidebar-backgro=
und)); }

.peer\/menu-button[data-size=3D"default"] ~ .peer-data-\[size\=3Ddefault\]\=
/menu-button\:top-1\.5 { top: 0.375rem; }

.peer\/menu-button[data-size=3D"lg"] ~ .peer-data-\[size\=3Dlg\]\/menu-butt=
on\:top-2\.5 { top: 0.625rem; }

.peer\/menu-button[data-size=3D"sm"] ~ .peer-data-\[size\=3Dsm\]\/menu-butt=
on\:top-1 { top: 0.25rem; }

.peer[data-variant=3D"inset"] ~ .peer-data-\[variant\=3Dinset\]\:min-h-\[ca=
lc\(100svh-theme\(spacing\.4\)\)\] { min-height: calc(-1rem + 100svh); }

.peer\/menu-button[data-active=3D"true"] ~ .peer-data-\[active\=3Dtrue\]\/m=
enu-button\:text-sidebar-accent-foreground { color: hsl(var(--sidebar-accen=
t-foreground)); }

.dark\:border-destructive:is(.dark *) { border-color: hsl(var(--destructive=
)); }

@media (width >=3D 640px) {
  .sm\:bottom-0 { bottom: 0px; }
  .sm\:right-0 { right: 0px; }
  .sm\:top-auto { top: auto; }
  .sm\:mt-0 { margin-top: 0px; }
  .sm\:flex { display: flex; }
  .sm\:w-auto { width: auto; }
  .sm\:max-w-sm { max-width: 24rem; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:flex-col { flex-direction: column; }
  .sm\:justify-end { justify-content: flex-end; }
  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: =
0; margin-right: calc(.5rem * var(--tw-space-x-reverse)); margin-left: calc=
(.5rem * calc(1 - var(--tw-space-x-reverse))); }
  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: =
0; margin-right: calc(1rem * var(--tw-space-x-reverse)); margin-left: calc(=
1rem * calc(1 - var(--tw-space-x-reverse))); }
  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: =
0; margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse))); margin-bott=
om: calc(0px * var(--tw-space-y-reverse)); }
  .sm\:rounded-lg { border-radius: var(--radius); }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:text-left { text-align: left; }
  .sm\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .sm\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .data-\[state\=3Dopen\]\:sm\:slide-in-from-bottom-full[data-state=3D"open=
"] { --tw-enter-translate-y: 100%; }
}

@media (width >=3D 768px) {
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:max-w-\[420px\] { max-width: 420px; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0px, 1fr)); }
  .md\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .md\:opacity-0 { opacity: 0; }
  .after\:md\:hidden::after { content: var(--tw-content); display: none; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:m-2 =
{ margin: 0.5rem; }
  .peer[data-state=3D"collapsed"][data-variant=3D"inset"] ~ .md\:peer-data-=
\[state\=3Dcollapsed\]\:peer-data-\[variant\=3Dinset\]\:ml-2 { margin-left:=
 0.5rem; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:ml-0=
 { margin-left: 0px; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:roun=
ded-xl { border-radius: 0.75rem; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:shad=
ow { --tw-shadow: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a; --tw-sha=
dow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-sh=
adow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-=
ring-shadow, 0 0 #0000), var(--tw-shadow); }
}

@media (width >=3D 1024px) {
  .lg\:sticky { position: sticky; }
  .lg\:top-10 { top: 2.5rem; }
  .lg\:col-span-1 { grid-column: span 1 / span 1; }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:col-span-3 { grid-column: span 3 / span 3; }
  .lg\:max-w-4xl { max-width: 56rem; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0px, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0px, 1fr)); }
  .lg\:gap-12 { gap: 3rem; }
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:text-5xl { font-size: 3rem; line-height: 1; }
}

@media (width >=3D 1280px) {
  .xl\:max-w-5xl { max-width: 64rem; }
}

.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) { backgro=
und-color: hsl(var(--accent)); }

.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected])=
:first-child { border-top-left-radius: calc(var(--radius)  - 2px); border-b=
ottom-left-radius: calc(var(--radius)  - 2px); }

.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):=
last-child { border-top-right-radius: calc(var(--radius)  - 2px); border-bo=
ttom-right-radius: calc(var(--radius)  - 2px); }

.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-sel=
ected].day-outside) { background-color: hsl(var(--accent) / .5); }

.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-se=
lected].day-range-end) { border-top-right-radius: calc(var(--radius)  - 2px=
); border-bottom-right-radius: calc(var(--radius)  - 2px); }

.\[\&\:has\(\[role\=3Dcheckbox\]\)\]\:pr-0:has([role=3D"checkbox"]) { paddi=
ng-right: 0px; }

.\[\&\>button\]\:hidden > button { display: none; }

.\[\&\>span\:last-child\]\:truncate > span:last-child { overflow: hidden; t=
ext-overflow: ellipsis; white-space: nowrap; }

.\[\&\>span\]\:line-clamp-1 > span { overflow: hidden; display: -webkit-box=
; -webkit-box-orient: vertical; -webkit-line-clamp: 1; }

.\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div { --tw-translate-y: -3p=
x; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotat=
e(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(=
var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.\[\&\>svg\]\:absolute > svg { position: absolute; }

.\[\&\>svg\]\:left-4 > svg { left: 1rem; }

.\[\&\>svg\]\:top-4 > svg { top: 1rem; }

.\[\&\>svg\]\:size-4 > svg { width: 1rem; height: 1rem; }

.\[\&\>svg\]\:h-2\.5 > svg { height: 0.625rem; }

.\[\&\>svg\]\:h-3 > svg { height: 0.75rem; }

.\[\&\>svg\]\:w-2\.5 > svg { width: 0.625rem; }

.\[\&\>svg\]\:w-3 > svg { width: 0.75rem; }

.\[\&\>svg\]\:shrink-0 > svg { flex-shrink: 0; }

.\[\&\>svg\]\:text-destructive > svg { color: hsl(var(--destructive)); }

.\[\&\>svg\]\:text-foreground > svg { color: hsl(var(--foreground)); }

.\[\&\>svg\]\:text-muted-foreground > svg { color: hsl(var(--muted-foregrou=
nd)); }

.\[\&\>svg\]\:text-sidebar-accent-foreground > svg { color: hsl(var(--sideb=
ar-accent-foreground)); }

.\[\&\>svg\~\*\]\:pl-7 > svg ~ * { padding-left: 1.75rem; }

.\[\&\>tr\]\:last\:border-b-0:last-child > tr { border-bottom-width: 0px; }

.\[\&\[data-state\=3Dopen\]\>svg\]\:rotate-180[data-state=3D"open"] > svg {=
 --tw-rotate: 180deg; transform: translate(var(--tw-translate-x), var(--tw-=
translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--=
tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .rechart=
s-cartesian-axis-tick text { fill: hsl(var(--muted-foreground)); }

.\[\&_\.recharts-cartesian-grid_line\[stroke\=3D\'\#ccc\'\]\]\:stroke-borde=
r\/50 .recharts-cartesian-grid line[stroke=3D"#ccc"] { stroke: hsl(var(--bo=
rder) / .5); }

.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-=
curve.recharts-tooltip-cursor { stroke: hsl(var(--border)); }

.\[\&_\.recharts-dot\[stroke\=3D\'\#fff\'\]\]\:stroke-transparent .recharts=
-dot[stroke=3D"#fff"] { stroke: rgba(0, 0, 0, 0); }

.\[\&_\.recharts-layer\]\:outline-none .recharts-layer { outline: rgba(0, 0=
, 0, 0) solid 2px; outline-offset: 2px; }

.\[\&_\.recharts-polar-grid_\[stroke\=3D\'\#ccc\'\]\]\:stroke-border .recha=
rts-polar-grid [stroke=3D"#ccc"] { stroke: hsl(var(--border)); }

.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radia=
l-bar-background-sector { fill: hsl(var(--muted)); }

.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts=
-rectangle.recharts-tooltip-cursor { fill: hsl(var(--muted)); }

.\[\&_\.recharts-reference-line_\[stroke\=3D\'\#ccc\'\]\]\:stroke-border .r=
echarts-reference-line [stroke=3D"#ccc"] { stroke: hsl(var(--border)); }

.\[\&_\.recharts-sector\[stroke\=3D\'\#fff\'\]\]\:stroke-transparent .recha=
rts-sector[stroke=3D"#fff"] { stroke: rgba(0, 0, 0, 0); }

.\[\&_\.recharts-sector\]\:outline-none .recharts-sector { outline: rgba(0,=
 0, 0, 0) solid 2px; outline-offset: 2px; }

.\[\&_\.recharts-surface\]\:outline-none .recharts-surface { outline: rgba(=
0, 0, 0, 0) solid 2px; outline-offset: 2px; }

.\[\&_p\]\:leading-relaxed p { line-height: 1.625; }

.\[\&_svg\]\:pointer-events-none svg { pointer-events: none; }

.\[\&_svg\]\:size-4 svg { width: 1rem; height: 1rem; }

.\[\&_svg\]\:shrink-0 svg { flex-shrink: 0; }

.\[\&_tr\:last-child\]\:border-0 tr:last-child { border-width: 0px; }

.\[\&_tr\]\:border-b tr { border-bottom-width: 1px; }

[data-side=3D"left"][data-collapsible=3D"offcanvas"] .\[\[data-side\=3Dleft=
\]\[data-collapsible\=3Doffcanvas\]_\&\]\:-right-2 { right: -0.5rem; }

[data-side=3D"left"][data-state=3D"collapsed"] .\[\[data-side\=3Dleft\]\[da=
ta-state\=3Dcollapsed\]_\&\]\:cursor-e-resize { cursor: e-resize; }

[data-side=3D"left"] .\[\[data-side\=3Dleft\]_\&\]\:cursor-w-resize { curso=
r: w-resize; }

[data-side=3D"right"][data-collapsible=3D"offcanvas"] .\[\[data-side\=3Drig=
ht\]\[data-collapsible\=3Doffcanvas\]_\&\]\:-left-2 { left: -0.5rem; }

[data-side=3D"right"][data-state=3D"collapsed"] .\[\[data-side\=3Dright\]\[=
data-state\=3Dcollapsed\]_\&\]\:cursor-w-resize { cursor: w-resize; }

[data-side=3D"right"] .\[\[data-side\=3Dright\]_\&\]\:cursor-e-resize { cur=
sor: e-resize; }
------MultipartBoundary--yq6765fiDXY66DPzXDja0aomfPDoeACPgDwEvJsm9d----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

@font-face { font-family: __nextjs-Geist; font-style: normal; font-weight: =
400 600; font-display: swap; src: url("/__nextjs_font/geist-latin-ext.woff2=
") format("woff2"); unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2=
D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, =
U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF; }

@font-face { font-family: "__nextjs-Geist Mono"; font-style: normal; font-w=
eight: 400 600; font-display: swap; src: url("/__nextjs_font/geist-mono-lat=
in-ext.woff2") format("woff2"); unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-=
2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U=
+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A=
7FF; }

@font-face { font-family: __nextjs-Geist; font-style: normal; font-weight: =
400 600; font-display: swap; src: url("/__nextjs_font/geist-latin.woff2") f=
ormat("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, =
U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2=
193, U+2212, U+2215, U+FEFF, U+FFFD; }

@font-face { font-family: "__nextjs-Geist Mono"; font-style: normal; font-w=
eight: 400 600; font-display: swap; src: url("/__nextjs_font/geist-mono-lat=
in.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-=
2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122,=
 U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
------MultipartBoundary--yq6765fiDXY66DPzXDja0aomfPDoeACPgDwEvJsm9d------
