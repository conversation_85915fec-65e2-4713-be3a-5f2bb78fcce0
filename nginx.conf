server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;

    # Enable gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_comp_level 6;
    gzip_min_length 1000;

    # Cache static assets with proper MIME types
    location ~* \.(jpg|jpeg|png|webp|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri $uri/ =404;
    }

    # Handle CSS and JS files
    location ~* \.(css|js)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri $uri/ =404;
    }

    # Handle assets directory explicitly
    location /assets/ {
        alias /usr/share/nginx/html/assets/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri $uri/ =404;
    }

    # Handle src/assets directory explicitly (for backward compatibility)
    location /src/assets/ {
        alias /usr/share/nginx/html/src/assets/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri $uri/ =404;
    }

    # Handle src/assets/images directory explicitly
    location /src/assets/images/ {
        alias /usr/share/nginx/html/src/assets/images/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri $uri/ =404;
    }

    # Handle specific image paths that are causing 404s
    location = /src/assets/images/features/image-18690.png {
        return 200;
    }

    location = /assets/png/image-18690.png {
        return 200;
    }

    location = /src/assets/images/partnerships/mission_3.jpg {
        rewrite ^ /assets/jpg/mission_3-BKHhCZit.jpg redirect;
    }

    location = /src/assets/images/partnerships/unsplash-s-3-h-qu-5-yjg1.png {
        rewrite ^ /assets/png/unsplash-s-3-h-qu-5-yjg1-D3Ynn7Ai.png redirect;
    }

    location = /src/assets/images/features/hubbletelescope.png {
        alias /usr/share/nginx/html/src/assets/images/features/hubbletelescope.png;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Access-Control-Allow-Origin "*";
    }

    # Handle hero background webp
    location = /assets/png/hero-background-DWw_hGQE.webp {
        rewrite ^ /assets/png/hero-background-DWw_hGQE.png redirect;
    }

    # Handle PNG files requested as WebP
    location ~ ^/assets/png/(.+)\.webp$ {
        try_files $uri /assets/png/$1.png =404;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Access-Control-Allow-Origin "*";
    }

    # Handle SPA routing - redirect all requests to index.html
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
